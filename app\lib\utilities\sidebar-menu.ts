import {
  Home,
  Landmark,
  Mail,
  UserRoundCheck,
  Wallet,
} from "lucide-react";
import { useFeatureFlags } from "../feature-flag/context/use-feature-flags";
import type { MenuItem } from "~/components/layout/sidebar/MenuItem";
import type { CompanyModuleState } from "~/features/companies/types";
import { DEFAULT_MODULE_STATE } from "~/features/companies/types";
import { strEntityTypeGuard } from "~/routes/_main._card.simplified-tax-return";

// Define the structure of the menu items
export default function useMenu(companyModules: CompanyModuleState): MenuItem[] {
  const moduleState = companyModules ?? DEFAULT_MODULE_STATE;
  const { isEnabled } = useFeatureFlags();
  const isInboxEnabled = isEnabled("Announcements");

  return [
    {
      icon: Home,
      label: "Dashboard",
      href: "/dashboard",
      show: true,
      count: false,
    },
    {
      icon: Mail,
      label: "Inbox",
      href: "/inbox",
      show: isInboxEnabled,
    },
    {
      icon: Landmark,
      label: "Simplified Tax Returns",
      href: "/simplified-tax-return",
      show: moduleState.simplifiedTaxReturn.enabled,
      entityTypeGuard: strEntityTypeGuard,
      children: [
        {
          label: "Create a Submission",
          href: "/simplified-tax-return/new",
          show: true,
        },
        {
          label: "Draft Submissions",
          href: "/simplified-tax-return/drafts",
          show: true,
        },
        {
          label: "Completed Submissions",
          href: "/simplified-tax-return/submissions",
          show: true,
        },
      ],
    },
    {
      icon: Landmark,
      label: "Basic Financial Report",
      href: "/basic-financial-report",
      show: moduleState.basicFinancialReport.enabled,
      children: [
        {
          label: "New submission",
          href: "/basic-financial-report/new",
          show: true,
        },
        {
          label: "Submissions",
          href: "/basic-financial-report/submissions",
          show: true,
        },
        {
          label: "Drafts",
          href: "/basic-financial-report/drafts",
          show: true,
        },
      ],
    },
    {
      icon: Landmark,
      label: "Economic Substance",
      href: "/economic-substance",
      show: moduleState.economicSubstanceBahamas.enabled || moduleState.economicSubstanceBVI.enabled,
      children: [
        {
          label: "New Submission",
          href: "/economic-substance/new",
          show: true,
        },
        {
          label: "Submissions",
          href: "/economic-substance/submissions",
          show: true,
        },
        {
          label: "Draft",
          href: "/economic-substance/drafts",
          show: true,
        },
      ],
    },
    {
      icon: UserRoundCheck,
      label: "Ownership & Officers",
      href: "/bo-directors",
      show: moduleState.boDirectors.enabled,
      children: [
        {
          label: "Beneficial Owners",
          href: "/bo-directors/beneficial-owner",
          show: true,
        },
        {
          label: "Statutory Officers",
          href: "/bo-directors/director",
          show: true,
        },
      ],
    },
    {
      icon: Wallet,
      label: "Payments",
      href: "/payments",
      show: true,
      children: [
        {
          label: "Pending Payments",
          href: "/payments/pending",
          show: true,
        },
        {
          label: "Invoices",
          href: "/payments/invoices",
          show: true,
        },
      ],
    },
  ] as MenuItem[];
}
