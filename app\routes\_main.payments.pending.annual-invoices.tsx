import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Alert, Button, Combobox, Form, FormControl, FormField, FormItem, FormMessage } from "@netpro/design-system";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { Form as RemixForm, useLoaderData, useSearchParams } from "@remix-run/react";
import { FilterX } from "lucide-react";
import { useForm } from "react-hook-form";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { Pagination } from "~/components/ui/filters/Pagination";
import type { Company } from "~/features/companies/api/get-companies.server";
import { getCompanies } from "~/features/companies/api/get-companies.server";
import { PendingPaymentsTabs } from "~/features/payments/components/PendingPaymentsTabs";
import { PendingSubmissionRow } from "~/features/payments/components/PendingSubmissionRow";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import type { paymentsFilterSchemaType } from "~/lib/payments/types/payment-schema";
import { paymentsFilterSchema } from "~/lib/payments/types/payment-schema";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { getPaginationParams } from "~/lib/utilities/get-pagination-params";
import type { ListSubmissionDTOPaginatedResponse } from "~/services/api-generated";
import { clientGetMasterClientSubmissions } from "~/services/api-generated";

const title = "Pending Payments" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Payments",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<{
  companies: Company[]
  unpaidSubmissions: ListSubmissionDTOPaginatedResponse
}> {
  const { userId, masterClient } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  let filters: {
    companyId?: string
    financialYear?: string
  } = {};
  const url = new URL(request.url);
  const searchParams = Object.fromEntries(url.searchParams.entries());
  const { pageNumber, pageSize } = await getPaginationParams({ request });

  if (searchParams) {
    // Validate params
    paymentsFilterSchema.parse(searchParams);
    // Set filters
    filters = {
      companyId: searchParams.company,
      financialYear: searchParams.financialYear,
    }
  }

  const { companies } = await getCompanies({
    masterClientId: masterClient.masterClientId as string,
    userId,
    params: {
      includeInactive: true,
    },
  });
  const { data, error } = await clientGetMasterClientSubmissions({
    headers: await authHeaders(request),
    path: {
      masterClientId: masterClient.masterClientId as string,
    },
    query: {
      IsPaid: false,
      HasInvoice: false,
      PageSize: pageSize,
      PageNumber: pageNumber,
      LegalEntityIds: filters.companyId ? [filters.companyId] : [],
      FinancialYears: filters.financialYear ? [Number(filters.financialYear)] : [],
      SubmissionStatuses: [SubmissionStatusNames.Submitted, SubmissionStatusNames.Revision],
    },
  });

  if (error) {
    console.error("Error fetching unpaid submissions:", error);
    throw new Error("Failed to fetch unpaid submissions");
  }

  return { companies, unpaidSubmissions: data };
}

export default function PendingPaymentsAnnualFees(): JSX.Element {
  const { companies, unpaidSubmissions } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const form = useForm<paymentsFilterSchemaType>({
    resolver: zodResolver(paymentsFilterSchema),
    defaultValues: {
      company: searchParams.get("company") ?? "all",
      financialYear: searchParams.get("financialYear") ?? "all",
    },
  });
  const companiesOptions = companies.map(company => ({
    value: company.companyId,
    label: company.companyName,
  }));
  companiesOptions.unshift({ value: "all", label: "All companies" });
  // Generate financial years options from 2019 to this year
  const availableFinancialYears = Array.from({ length: new Date().getFullYear() + 1 - 2019 }, (_, i) => 2019 + i);
  const financialYearsOptions = [
    { value: "all", label: "All financial years" },
    ...availableFinancialYears.map(year => ({ value: year.toString(), label: year.toString() })),
  ];

  function handleSubmitFilter(data: paymentsFilterSchemaType): void {
    setSearchParams((prev) => {
      if (data.company === "all" || !data.company) {
        prev.delete("company");
      } else if (data.company) {
        prev.set("company", data.company);
      }

      if (data.financialYear === "all" || !data.financialYear) {
        prev.delete("financialYear");
      } else if (data.financialYear) {
        prev.set("financialYear", data.financialYear);
      }

      prev.delete("page");

      return prev;
    });
  }

  function clearFilters(): void {
    form.setValue("company", "all");
    form.setValue("financialYear", "all");
    form.handleSubmit(handleSubmitFilter)();
  }

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-1">
        <Form {...form}>
          <RemixForm noValidate>
            <PendingPaymentsTabs currentTab="annual-invoices" />
            <Alert variant="info" title="Important note" dismissible>
              The submission fees are incorporated into the companies' annual invoices, which remain outstanding.
            </Alert>
            <div className="grid md:grid-cols-4 gap-2 mt-5">
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="w-full">
                      <Combobox
                        placeholder="All companies"
                        searchText="Search for a companies..."
                        noResultsText="No companies found."
                        items={companiesOptions}
                        onChange={(value) => {
                          field.onChange(value);
                          form.handleSubmit(handleSubmitFilter)();
                        }}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="financialYear"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="w-full">
                      <Combobox
                        placeholder="All financial years"
                        searchText="Search for a financial year..."
                        noResultsText="No financial years found."
                        items={financialYearsOptions}
                        onChange={(value) => {
                          field.onChange(value);
                          form.handleSubmit(handleSubmitFilter)();
                        }}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <Button type="button" onClick={clearFilters} className="h-9">
                  <FilterX className="mr-2 size-4" />
                  Reset filters
                </Button>
              </div>
            </div>
          </RemixForm>
        </Form>
        <div className="mt-8">
          {unpaidSubmissions.data && unpaidSubmissions.data.length > 0 && (
            <>
              {unpaidSubmissions.data.map(submission => (
                <PendingSubmissionRow submission={submission} key={submission.id} />
              ))}
              <Pagination totalItems={unpaidSubmissions.totalItemCount as number} />
            </>
          )}
          {unpaidSubmissions.data && unpaidSubmissions.data.length === 0 && (
            <CenteredMessage title="No pending payments found" />
          )}
        </div>
      </div>
    </div>
  )
}
