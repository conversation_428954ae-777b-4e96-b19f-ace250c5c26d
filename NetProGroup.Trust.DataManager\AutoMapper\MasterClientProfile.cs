﻿// <copyright file="MasterClientProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository.Builders;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// Provides mapping configurations for the <see cref="MasterClient"/> domain model to its corresponding DTOs.
    /// </summary>
    public class MasterClientProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientProfile"/> class.
        /// Configures mappings for <see cref="MasterClient"/> to <see cref="MasterClientsSearchResultDTO"/>.
        /// </summary>
        public MasterClientProfile()
        {
            CreateMap<MasterClient, MasterClientsSearchResultDTO>()
                .ForMember(dest => dest.MasterClientId, opt => opt.MapFrom(
                    src => src.Id))
                .ForMember(dest => dest.MasterClientCode, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.Companies, opt => opt.MapFrom(src => src.LegalEntities.ToList()))
                .ForMember(dest => dest.HasActiveCompanies, opt => opt.MapFrom(MasterClientExpressionBuilder.GetMasterClientIsActivePredicate()))
                .ForMember(dest => dest.HasActiveRequestsForInformation, opt => opt.MapFrom(src => src.LegalEntities != null &&
                            src.LegalEntities.Any(le =>
                                le.Submissions != null &&
                                le.Submissions.Any(s =>
                                    s.RequestsForInformation != null &&
                                    s.RequestsForInformation.Any(rfi => rfi.Status == RequestForInformationStatus.Active)))));
        }
    }
}