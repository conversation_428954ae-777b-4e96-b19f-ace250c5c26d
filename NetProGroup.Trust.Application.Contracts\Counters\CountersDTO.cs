﻿// <copyright file="CountersDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Inboxes;

namespace NetProGroup.Trust.Application.Contracts.Counters
{
    /// <summary>
    /// Represents the counters for various application metrics.
    /// </summary>
    public class CountersDTO
    {
        /// <summary>
        /// Gets or sets the invoice information count.
        /// </summary>
        public InvoiceInfoDTO InvoiceCounter { get; set; }

        /// <summary>
        /// Gets or sets the count of pending RFIs (Requests for Information).
        /// </summary>
        public int PendingRFIsCounter { get; set; }

        /// <summary>
        /// Gets or sets the inbox information.
        /// </summary>
        public InboxInfoDTO InboxCounter { get; set; }
    }
}
