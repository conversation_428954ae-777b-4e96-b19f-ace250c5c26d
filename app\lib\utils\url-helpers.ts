/**
 * Utility functions for URL manipulation and parameter extraction
 */

export type UrlRedirectionParams = {
  setCompanyId?: string
  setMasterClientId?: string
};

/**
 * Extracts setCompanyId and setMasterClientId parameters from a URL
 * @param url - The URL to extract parameters from
 * @returns Object containing the extracted parameters
 */
export function extractUrlParams(url: string): UrlRedirectionParams {
  try {
    const urlObj = new URL(url);

    return {
      setCompanyId: urlObj.searchParams.get("setCompanyId") || undefined,
      setMasterClientId: urlObj.searchParams.get("setMasterClientId") || undefined,
    };
  } catch {
    return {};
  }
}

/**
 * Removes specific parameters from a URL
 * @param url - The URL to modify
 * @param paramsToRemove - Array of parameter names to remove
 * @returns The URL with specified parameters removed
 */
export function removeParamsFromUrl(url: string, paramsToRemove: string[]): string {
  try {
    const urlObj = new URL(url);
    paramsToRemove.forEach(param => urlObj.searchParams.delete(param));

    return urlObj.toString();
  } catch {
    return url;
  }
}

/**
 * Checks if a URL contains both setCompanyId and setMasterClientId parameters
 * @param url - The URL to check
 * @returns True if the URL contains both required parameters
 */
export function hasRedirectionParams(url: string): boolean {
  const params = extractUrlParams(url);

  return !!(params.setCompanyId && params.setMasterClientId);
}
