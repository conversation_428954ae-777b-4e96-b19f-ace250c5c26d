import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Combobox,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useLoaderData, useNavigation, useParams } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { FileUploadDialog } from "../../dialogs/financial-period/FileUploadDialog";
import { ParentEntityDialog } from "../../dialogs/tax-payer-identification.tsx/ParentEntityDialog";
import { ParentEntityTable } from "../../tables/tax-payer-identification/ParentEntityTable";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { type FileSchemaType, fileSchema } from "~/lib/economic-substance/types/bahamas/file-schema";
import type {
  ParentEntitySchemaType,
  TaxPayerIdentificationSchemaType,
} from "~/lib/economic-substance/types/bahamas/tax-payer-identification-schema";
import {
  currency,
  parentEntitySchema,
  taxPayerIdentificationSchema,
} from "~/lib/economic-substance/types/bahamas/tax-payer-identification-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Currency } from "~/lib/economic-substance/utilities/currencies";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { getCountryOptions } from "~/lib/utilities/countries";
import { convertMappedDocumentsToFileObject, groupDocuments } from "~/lib/utilities/files";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { EconomicSubstanceData } from "~/routes/_main._card.economic-substance.$id.$pageName";

export type FileFieldName = keyof Pick<TaxPayerIdentificationSchemaType, "files_BusinessLicense" | "files_TaxPayerEvidence">;
export type ArrayFieldName = keyof Pick<TaxPayerIdentificationSchemaType, "ultimateParentEntities" | "immediateParentEntities">;

export function TaxPayerIdentification(): ReactNode {
  const { id } = useParams()
  const loader = useLoaderData<EconomicSubstanceData>()
  const { submissionData } = useSubmission();
  const navigation = useNavigation()
  const fetcher = useFetcher()
  const countryOptions = useMemo(() => getCountryOptions(), []);
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading" || fetcher.state === "submitting"
  const data = useMemo(() => {
    const newData: Record<string, any> = {};
    for (const key in submissionData[Pages.TAX_PAYER_IDENTIFICATION]) {
      const element = submissionData[Pages.TAX_PAYER_IDENTIFICATION][key];
      if (element !== "undefined") {
        newData[key] = element;
      }
    }

    return newData as TaxPayerIdentificationSchemaType;
  }, [submissionData]);
  const form = useForm<TaxPayerIdentificationSchemaType>({
    resolver: zodResolver(taxPayerIdentificationSchema),
    shouldFocusError: false,
    defaultValues: {
      isBahamianResident: data?.isBahamianResident ?? "",
      isInvestmentFund: data?.isInvestmentFund ?? "",
      entityGrossTotalAnnualIncomeAmount: data?.entityGrossTotalAnnualIncomeAmount ?? "",
      isPartOfMneGroup: data?.isPartOfMneGroup ?? "",
      mneGroupName: data?.mneGroupName ?? "",
      intendsToClaimTaxResidencyOutsideBahamas: data?.intendsToClaimTaxResidencyOutsideBahamas ?? "",
      taxResidencyJurisdiction: data?.taxResidencyJurisdiction ?? "",
      taxPayerIdentificationNumber: data?.taxPayerIdentificationNumber ?? "",
      hasUltimateParentEntity: data?.hasImmediateParentEntity ?? "",
      ultimateParentEntities: data?.ultimateParentEntities ?? [],
      immediateParentEntities: data?.immediateParentEntities ?? [],
      hasImmediateParentEntity: data?.hasImmediateParentEntity ?? "",
    },
  });
  const { formState, setValue, watch } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: TaxPayerIdentificationSchemaType): void {
    /*
     * When using JSON.stringify undefined values are not included in the object
     * That's why is needed to type: "undefined"
     */
    const formattedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => {
        return [key, value === undefined ? "undefined" : value];
      }),
    );
    fetcher.submit({ data: JSON.stringify(formattedData) }, {
      method: "post",
    });
  }

  // PARENT ENTITY ARRAY SECTION
  const parentEntityForm = useForm<ParentEntitySchemaType>({
    resolver: zodResolver(parentEntitySchema),
    defaultValues: {
      name: "",
      alternativeName: "",
      incorporationNumber: "",
      jurisdictionOfFormation: "",
      taxpayerIdentificationNumber: "",
    },
  });
  const [parentEntityIndex, setParentEntityIndex] = useState<number | undefined>();
  const [arrayFieldName, setArrayFieldName] = useState<ArrayFieldName | undefined>()
  const [openEntityDialog, setOpenEntityDialog] = useState(false)
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const ultimateParentsArray = useFieldArray({
    control: form.control,
    name: "ultimateParentEntities",
    keyName: "formArrayId",
  });
  const immediateParentsArray = useFieldArray({
    control: form.control,
    name: "immediateParentEntities",
    keyName: "formArrayId",
  });

  function addEntityParent(fieldName: ArrayFieldName): void {
    parentEntityForm.reset();
    setArrayFieldName(fieldName)
    setParentEntityIndex(undefined);
    setOpenEntityDialog(true);
  }

  function onSelect(fieldName: ArrayFieldName, income: ParentEntitySchemaType, index: number): void {
    setArrayFieldName(fieldName)
    parentEntityForm.reset(income, { keepDefaultValues: true });
    setParentEntityIndex(index);
    setOpenEntityDialog(true);
  }

  function onDelete(): void {
    if (arrayFieldName === "ultimateParentEntities") {
      ultimateParentsArray.remove(parentEntityIndex);
    }

    if (arrayFieldName === "immediateParentEntities") {
      immediateParentsArray.remove(parentEntityIndex);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: ArrayFieldName, index: number): void {
    setArrayFieldName(fieldName)
    setParentEntityIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setParentEntityIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  function onSubmitParentEntity(data: ParentEntitySchemaType): void {
    if (arrayFieldName === "ultimateParentEntities") {
      if (parentEntityIndex !== undefined) {
        ultimateParentsArray.update(parentEntityIndex, data);
      } else {
        ultimateParentsArray.append(data);
      }
    }

    if (arrayFieldName === "immediateParentEntities") {
      if (parentEntityIndex !== undefined) {
        immediateParentsArray.update(parentEntityIndex, data);
      } else {
        immediateParentsArray.append(data);
      }
    }

    setOpenEntityDialog(false);
  }

  // FILE HANDLING SECTION
  const documentsArray = convertMappedDocumentsToFileObject(loader.mappedDocuments)
  const [fileFieldName, setFileFieldName] = useState<FileFieldName | undefined>()
  const fetcherCreateDocument = useFetcher<string[]>();
  const fetcherUpdateDocument = useFetcher();
  const [openUploadFile, setOpenUploadFile] = useState(false);
  const fileForm = useForm<FileSchemaType>({
    resolver: zodResolver(fileSchema),
    shouldFocusError: false,
  });

  function onSubmitFile(data: FileSchemaType): void {
    if (fileFieldName) {
      form.setValue(fileFieldName, data)
      const formData = new FormData()
      const filesData = data.files
      filesData.forEach((fileData) => {
        formData.append("files", fileData)
      });
      formData.append("location", location.pathname)
      fetcherCreateDocument.submit(formData, {
        action: "/documents/create",
        encType: "multipart/form-data",
        method: "post",
      })
    }

    setOpenUploadFile(false)
  }

  const onOpenUploadDialog = (fieldName: FileFieldName): void => {
    setFileFieldName(fieldName)
    const files = form.getValues(fieldName)
    fileForm.reset(files, { keepDefaultValues: true })
    setOpenUploadFile(true)
  }

  useEffect(() => {
    const documentIds = fetcherCreateDocument.data

    if (documentIds && fileFieldName) {
      const data: Record<string, string> = {}
      documentIds.forEach((documentId, index) => {
        /*
         * To ensure proper document handling, all document keys must:
         * - Begin with "documentId"
         * - Be separated by underscores ("_")
         * - End with an index to uniquely identify each document
         */
        const keyName = `documentId_${fileFieldName}_${index}`
        data[keyName] = documentId
      });
      fetcherUpdateDocument.submit({
        data: JSON.stringify(data),
        location: location.pathname,
      }, {
        action: `/economic-substance/${id}/pages/${Pages.TAX_PAYER_IDENTIFICATION}/documents/update`,
        method: "post",
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcherCreateDocument.data])

  useEffect(() => {
    if (documentsArray) {
      // Group documents by type
      const businessLicenseFiles = groupDocuments("files_BusinessLicense", documentsArray);
      const taxPayerEvidenceFiles = groupDocuments("files_TaxPayerEvidence", documentsArray);

      // Update form values
      setValue("files_BusinessLicense", businessLicenseFiles.length > 0 ? { files: businessLicenseFiles } : undefined);
      setValue("files_TaxPayerEvidence", taxPayerEvidenceFiles.length > 0 ? { files: taxPayerEvidenceFiles } : undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setValue]);

  // Watch form values
  const watchIsBahamianResident = watch("isBahamianResident")
  const watchFilesBusinessLicense = watch("files_BusinessLicense")
  const watchFilesTaxPayerEvidence = watch("files_TaxPayerEvidence")
  const watchIsInvestmentFund = watch("isInvestmentFund")
  const watchIsPartOfMneGroup = watch("isPartOfMneGroup")
  const watchIntendsToClaimTaxResidencyOutsideBahamas = watch("intendsToClaimTaxResidencyOutsideBahamas")
  const watchHasImmediateParentEntity = watch("hasImmediateParentEntity")
  const watchHasUltimateParentEntity = watch("hasUltimateParentEntity")
  // Handle form values when changing fields
  const resetIntendsToClaimTaxResidencyOutsideBahamasDependentFields = () => {
    setValue("taxResidencyJurisdiction", "")
    setValue("taxPayerIdentificationNumber", "")
    setValue("files_TaxPayerEvidence", undefined)
  }
  const resetIsInvestmentFundDependentFields = () => {
    setValue("entityGrossTotalAnnualIncomeAmount", "")
    setValue("isPartOfMneGroup", "")
    setValue("mneGroupName", "")
    setValue("intendsToClaimTaxResidencyOutsideBahamas", "")
    setValue("hasUltimateParentEntity", "")
    setValue("ultimateParentEntities", [])
    setValue("hasImmediateParentEntity", "")
    setValue("immediateParentEntities", [])
    resetIntendsToClaimTaxResidencyOutsideBahamasDependentFields()
  }
  const handleIsBahamianResidentChange = (value: string) => {
    setValue("isBahamianResident", value as TaxPayerIdentificationSchemaType["isBahamianResident"])
    if (value === "false") {
      setValue("files_BusinessLicense", undefined)
      setValue("isInvestmentFund", "")
    }

    if (value === "true") {
      resetIsInvestmentFundDependentFields()
    }
  }
  const handleIsInvestmentFundChange = (value: string) => {
    setValue("isInvestmentFund", value as TaxPayerIdentificationSchemaType["isInvestmentFund"])
    if (value === "true") {
      resetIsInvestmentFundDependentFields()
    }
  }
  const handleIsPartOfMneGroupChange = (value: string) => {
    setValue("isPartOfMneGroup", value as TaxPayerIdentificationSchemaType["isPartOfMneGroup"])
    if (value === "false") {
      setValue("mneGroupName", "")
    }
  }
  const handleIntendsToClaimTaxResidencyOutsideBahamasChange = (value: string) => {
    setValue("intendsToClaimTaxResidencyOutsideBahamas", value as TaxPayerIdentificationSchemaType["intendsToClaimTaxResidencyOutsideBahamas"])
    if (value === "false") {
      resetIntendsToClaimTaxResidencyOutsideBahamasDependentFields()
    }
  }
  const handleHasUltimateParentEntityChange = (value: string) => {
    setValue("hasUltimateParentEntity", value as TaxPayerIdentificationSchemaType["hasUltimateParentEntity"])
    if (value === "false") {
      setValue("ultimateParentEntities", [])
    }
  }
  const handleHasImmediateParentEntityChange = (value: string) => {
    setValue("hasImmediateParentEntity", value as TaxPayerIdentificationSchemaType["hasImmediateParentEntity"])
    if (value === "false") {
      setValue("immediateParentEntities", [])
    }
  }

  return (
    <div className="relative">
      {fileFieldName && (
        <FileUploadDialog
          form={fileForm}
          open={openUploadFile}
          setOpen={setOpenUploadFile}
          onSubmit={onSubmitFile}
        />
      )}
      {arrayFieldName && (
        <ParentEntityDialog
          open={openEntityDialog}
          setOpen={setOpenEntityDialog}
          form={parentEntityForm}
          onSubmit={onSubmitParentEntity}
          type={arrayFieldName}
        />
      )}
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCloseDeleteConfirmation}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>
              Yes,
              delete this property
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Form {...form}>
        <RemixForm
          onSubmit={form.handleSubmit(onSubmit, onError)}
          noValidate
          id={ECONOMIC_SUBSTANCE_FORM_ID}
          className="space-y-5"
        >
          <div className="flex-col space-y-7">
            <FormField
              control={form.control}
              name="isBahamianResident"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Are you a 100% Bahamian/resident owned and Core Income Generated Activity
                      (CIGA) conducted in the Bahamas?*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={handleIsBahamianResidentChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchIsBahamianResident === "true" && (
              <FormField
                control={form.control}
                name="files_BusinessLicense"
                render={() => (
                  <FormItem className="flex flex-col">
                    <FormLabel>
                      <p className="flex gap-1">
                        Upload Business License, if you have one (PDF only)
                      </p>
                    </FormLabel>
                    <FormControl className="md:w-fit sm:w-full">
                      <Button
                        size="sm"
                        onClick={() => onOpenUploadDialog("files_BusinessLicense")}
                        type="button"
                        disabled={isSubmitting}
                      >
                        <Plus className="size-4 mr-2" />
                        {`${watchFilesBusinessLicense ? "File uploaded" : "  Upload file"}`}
                      </Button>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            {watchIsBahamianResident === "false" && (
              <>
                <FormField
                  control={form.control}
                  name="isInvestmentFund"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>
                        <p className="flex gap-1">
                          Are you an Investment Fund according to the Investment Funds
                          Act,2019 (No. 2 of 2019)?*
                        </p>
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={handleIsInvestmentFundChange}
                          value={field.value}
                          invalid={!!fieldState.error}
                          className="flex flex-row space-x-2"
                          disabled={isSubmitting}
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Yes
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              No
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {watchIsInvestmentFund === "false" && (
                  <>
                    <p className="font-bold text-md">Entity Gross Total Annual Income</p>
                    <FormField
                      control={form.control}
                      name="entityGrossTotalAnnualIncomeAmount"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormControl className="md:w-1/3 sm:w-full">
                            <CurrencyInput
                              currencyName={Currency.USD}
                              invalid={!!fieldState.error}
                              {...field}
                              disabled={isSubmitting}
                              type="number"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="isPartOfMneGroup"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>
                            <p className="flex gap-1">
                              Are you a part of the MNE Group?*
                            </p>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={handleIsPartOfMneGroupChange}
                              value={field.value}
                              invalid={!!fieldState.error}
                              className="flex flex-row space-x-2"
                              disabled={isSubmitting}
                            >
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="true" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="false" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {watchIsPartOfMneGroup === "true" && (
                      <FormField
                        control={form.control}
                        name="mneGroupName"
                        render={({ field, fieldState }) => (
                          <FormItem>
                            <FormLabel>
                              <p className="flex gap-1">
                                Name of MNE Group*
                              </p>
                            </FormLabel>
                            <FormControl className="md:w-1/3 sm:w-full">
                              <Input
                                invalid={!!fieldState.error}
                                {...field}
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                    <FormField
                      control={form.control}
                      name="intendsToClaimTaxResidencyOutsideBahamas"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>
                            <p className="flex gap-1">
                              Does the entity intend to make a claim of tax residency
                              outside of The Bahamas?*
                            </p>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={handleIntendsToClaimTaxResidencyOutsideBahamasChange}
                              value={field.value}
                              invalid={!!fieldState.error}
                              className="flex flex-row space-x-2"
                              disabled={isSubmitting}
                            >
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="true" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="false" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {watchIntendsToClaimTaxResidencyOutsideBahamas === "true" && (
                      <>
                        <FormField
                          control={form.control}
                          name="taxResidencyJurisdiction"
                          render={({ field, fieldState }) => (
                            <FormItem>
                              <Tooltip delayDuration={0}>
                                <FormLabel>
                                  <p className="flex gap-1">
                                    Jurisdiction in which the entity is tax
                                    resident*
                                    <TooltipTrigger asChild>
                                      <Info className="flex shrink-0 size-4" />
                                    </TooltipTrigger>
                                  </p>
                                </FormLabel>
                                <TooltipContent
                                  className="w-96 p-5 space-y-3 font-inter"
                                  side="right"
                                >
                                  <p>
                                    If yes, please provide evidence of tax residency
                                    in another jurisdiction which
                                    may include a certification by the relevant tax
                                    authority or copy of the annual
                                    tax return payment receipt. If not, ES ACT deems
                                    all companies tax resident in the Bahamas.
                                    Only those companies that are actually tax
                                    resident in another jurisdiction will be
                                    required
                                    to submit evidence thereto.
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                              <FormControl className="md:w-1/3 sm:w-full">
                                <Combobox
                                  placeholder="Select a country"
                                  searchText="Search..."
                                  noResultsText="No countries found."
                                  items={countryOptions}
                                  onChange={field.onChange}
                                  value={field.value}
                                  invalid={!!fieldState.error}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="taxPayerIdentificationNumber"
                          render={({ field, fieldState }) => (
                            <FormItem>
                              <FormLabel>
                                <p className="flex gap-1">
                                  Tax Payer Identification Number ("TIN") or other
                                  identification reference number*
                                </p>
                              </FormLabel>
                              <FormControl className="md:w-1/3 sm:w-full">
                                <Input
                                  invalid={!!fieldState.error}
                                  {...field}
                                  disabled={isSubmitting}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="files_TaxPayerEvidence"
                          render={() => (
                            <FormItem className="flex flex-col">
                              <FormLabel>
                                <p className="flex gap-1">
                                  Please upload evidence
                                </p>
                              </FormLabel>
                              <FormControl className="md:w-fit sm:w-full">
                                <Button
                                  size="sm"
                                  onClick={() => onOpenUploadDialog("files_TaxPayerEvidence")}
                                  type="button"
                                  disabled={isSubmitting}
                                >
                                  <Plus className="size-4 mr-2" />
                                  {`${watchFilesTaxPayerEvidence ? "File uploaded" : "  Upload file"}`}
                                </Button>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                    <FormField
                      control={form.control}
                      name="hasUltimateParentEntity"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <Tooltip delayDuration={0}>
                            <FormLabel>
                              <p className="flex gap-1">
                                Does entity have an ultimate parent entity?*
                                <TooltipTrigger asChild>
                                  <Info className="flex shrink-0 size-4" />
                                </TooltipTrigger>
                              </p>
                            </FormLabel>
                            <TooltipContent
                              className="w-96 p-5 space-y-3 font-inter"
                              side="right"
                            >
                              <p>
                                A “ultimate parent” means an entity that meets
                                the following criteria:
                              </p>
                              <p>
                                (a) it owns directly or indirectly a sufficient
                                interest in the corporate and legal
                                entity such that it is required to prepare
                                consolidated financial statements under
                                accounting principles generally applied in its
                                jurisdiction of residence, or would
                                be so required if its equity interest were
                                traded on a public securities exchange
                                in its jurisdiction of residence; and
                              </p>
                              <p>
                                (b) here is no other entity that owns directly
                                or indirectly an interest described
                                in paragraph (a) above in the first mentioned
                                entity.
                              </p>
                            </TooltipContent>
                          </Tooltip>
                          <FormControl>
                            <RadioGroup
                              onValueChange={handleHasUltimateParentEntityChange}
                              value={field.value}
                              invalid={!!fieldState.error}
                              className="flex flex-row space-x-2"
                              disabled={isSubmitting}
                            >
                              <FormItem
                                className="flex items-center space-x-2 space-y-0"
                              >
                                <FormControl>
                                  <RadioGroupItem value="true" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem
                                className="flex items-center space-x-2 space-y-0"
                              >
                                <FormControl>
                                  <RadioGroupItem value="false" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {watchHasUltimateParentEntity === "true" && (
                      <>
                        <FormField
                          name="ultimateParentEntities"
                          control={form.control}
                          render={({ fieldState }) => (
                            <FormItem>
                              {fieldState.invalid
                              && <ValidationAlert fieldState={fieldState} />}
                              <FormControl>
                                <ParentEntityTable
                                  disabled={isSubmitting}
                                  parentEntities={ultimateParentsArray.fields}
                                  onSelect={(income, index) => onSelect("ultimateParentEntities", income, index)}
                                  onDelete={index => onOpenDeleteConfirmation("ultimateParentEntities", index)}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <div className="flex justify-end">
                          <Button
                            size="sm"
                            onClick={() => addEntityParent("ultimateParentEntities")}
                            type="button"
                            disabled={isSubmitting}
                          >
                            <Plus className="mr-2 size-4 text-white" />
                            Add ultimate parent
                          </Button>
                        </div>
                      </>
                    )}
                    <FormField
                      control={form.control}
                      name="hasImmediateParentEntity"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <Tooltip delayDuration={0}>
                            <FormLabel>
                              <p className="flex gap-1">
                                Does entity have an immediate parent entity?*
                                <TooltipTrigger asChild>
                                  <Info className="flex shrink-0 size-4" />
                                </TooltipTrigger>
                              </p>
                            </FormLabel>
                            <TooltipContent
                              className="w-96 p-5 space-y-3 font-inter"
                              side="right"
                            >
                              <p>
                                A “immediate parent” means any entity(ies) that
                                own(s) directly 25%
                                or more of the ownership or voting interests in
                                the corporate and legal
                                entity and the immediate parent may be a
                                corporate or a non-corporate entity,
                                for example a partnership.
                              </p>
                            </TooltipContent>
                          </Tooltip>
                          <FormControl>
                            <RadioGroup
                              onValueChange={handleHasImmediateParentEntityChange}
                              value={field.value}
                              invalid={!!fieldState.error}
                              className="flex flex-row space-x-2"
                              disabled={isSubmitting}
                            >
                              <FormItem
                                className="flex items-center space-x-2 space-y-0"
                              >
                                <FormControl>
                                  <RadioGroupItem value="true" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem
                                className="flex items-center space-x-2 space-y-0"
                              >
                                <FormControl>
                                  <RadioGroupItem value="false" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {watchHasImmediateParentEntity === "true" && (
                      <>
                        <FormField
                          name="immediateParentEntities"
                          control={form.control}
                          render={({ fieldState }) => (
                            <FormItem>
                              {fieldState.invalid
                              && <ValidationAlert fieldState={fieldState} />}
                              <FormControl>
                                <ParentEntityTable
                                  disabled={isSubmitting}
                                  parentEntities={immediateParentsArray.fields}
                                  onSelect={(income, index) => onSelect("immediateParentEntities", income, index)}
                                  onDelete={index => onOpenDeleteConfirmation("immediateParentEntities", index)}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <div className="flex justify-end">
                          <Button
                            size="sm"
                            onClick={() => addEntityParent("immediateParentEntities")}
                            type="button"
                            disabled={isSubmitting}
                          >
                            <Plus className="mr-2 size-4 text-white" />
                            Add immediate parent
                          </Button>
                        </div>
                      </>
                    )}
                  </>
                )}
              </>
            )}

          </div>
        </RemixForm>
      </Form>
      <LoadingState
        isLoading={fetcher.state === "submitting" || fetcher.state === "loading"}
        message="Saving..."
      />
    </div>
  )
}
