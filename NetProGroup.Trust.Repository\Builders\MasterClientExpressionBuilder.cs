// <copyright file="MasterClientExpressionBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Enums;
using System.Linq.Expressions;

namespace NetProGroup.Trust.Domain.Repository.Builders
{
    /// <summary>
    /// Provides builder for filter expressions.
    /// </summary>
    public static class MasterClientExpressionBuilder
    {
        /// <summary>
        /// Gets the predicate expression to filter master clients that are active.
        /// </summary>
        /// <returns>The Get Master Client Is Active Predicate.</returns>
        public static Expression<Func<MasterClient, bool>> GetMasterClientIsActivePredicate()
            => mc => mc.LegalEntities.Any(le => le.IsActive && le.OnboardingStatus == OnboardingStatus.Approved);
    }
}
