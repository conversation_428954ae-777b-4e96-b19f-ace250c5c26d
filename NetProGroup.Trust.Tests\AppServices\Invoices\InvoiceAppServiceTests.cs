﻿// <copyright file="InvoiceAppServiceTests.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Invoices
{
    [TestFixture()]
    public class InboxAppServiceTests : TestBase
    {
        private IInvoiceAppService _invoiceAppService;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private ILegalEntityModulesRepository _legalEntityModulesRepository;
        private IInvoiceRepository _invoiceRepository;
        private ISubmissionsManager _submissionManager;

        private LegalEntity _legalEntity;

        protected override void ConfigureTestServices(IServiceCollection services) => base.ConfigureTestServices(services);

        [SetUp]
        public void SetUpAsync()
        {
            _invoiceAppService = _server.Services.GetRequiredService<IInvoiceAppService>();
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _legalEntityModulesRepository = _server.Services.GetRequiredService<ILegalEntityModulesRepository>();
            _invoiceRepository = _server.Services.GetRequiredService<IInvoiceRepository>();

            _submissionManager = _server.Services.GetRequiredService<ISubmissionsManager>();
        }

        [Test]
        public async Task GetUnpaidInvoicesCountAsync_WhenUnpaidInvoicesExist_ShouldReturnCorrectCount()
        {
            // Arrange
            SetWorkContextUser(ClientUser);
            await SeedLegalEntityForSTRAsync();

            //Start submission
            var submission1 = await _submissionManager.StartSubmissionAsync(new StartSubmissionDTO
            {
                LegalEntityId = _legalEntity.Id,
                ModuleId = ModuleStrId,
                FinancialYear = 2019
            });

            var submission2 = await _submissionManager.StartSubmissionAsync(new StartSubmissionDTO
            {
                LegalEntityId = _legalEntity.Id,
                ModuleId = ModuleStrId,
                FinancialYear = 2024
            });

            //Submit submissions 1 and 2
            await _submissionManager.SubmitSubmissionAsync(new SubmitSubmissionDTO
            {
                SubmissionId = submission1.Id
            }, ClientUser.Id, ClientUser.Email); 

            await _submissionManager.SubmitSubmissionAsync(new SubmitSubmissionDTO
            {
                SubmissionId = submission2.Id
            }, ClientUser.Id, ClientUser.Email);

            // Act
            var unpaidCount = await _invoiceAppService.GetUnpaidInvoicesCountAsync(_masterClient.Id);

            // Assert
            unpaidCount.UnpaidInvoices.Should().Be(1);
            unpaidCount.UnpaidAnnualInvoices.Should().Be(1);
        }

        #region private

        /// <summary>
        /// Setup testing data needed to execute the tests.
        /// </summary>
        private async Task SeedLegalEntityForSTRAsync()
        {
            // Create a legal entity for Nevis
            _legalEntity = new LegalEntity
            {
                MasterClientId = _masterClient.Id,
                Name = "Nevis Company",
                Code = "Legal entity code",
                LegacyCode = "Nevis-123",
                JurisdictionId = JurisdictionNevisId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = "1235346567",
                ReferralOffice = "Nevis Office",
                IncorporationDate = DateTime.Now
            };
            await _legalEntitiesRepository.InsertAsync(_legalEntity, true);


            await _legalEntityModulesRepository.InsertAsync(new LegalEntityModule(_legalEntity.Id, ModuleStrId) { IsApproved = true, IsEnabled = true }, saveChanges: true);
        }

        #endregion
    }
}