﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Submissions
{
    [TestFixture()]
    public class SubmissionAppServiceTests : TestBase
    {
        private ISubmissionsAppService _sut;
        private IRequestForInformationManager _rfiManager;
        private ISubmissionsManager _submissionsManager;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private ILegalEntityModulesRepository _legalEntityModulesRepository;
        private IInvoiceRepository _invoiceRepository;

        private Mock<ILockManager> _mock;

        [SetUp]
        public void SetUp()
        {
            _sut = _server.Services.GetRequiredService<ISubmissionsAppService>();
            _rfiManager = _server.Services.GetRequiredService<IRequestForInformationManager>();
            _submissionsManager = _server.Services.GetRequiredService<ISubmissionsManager>();

            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _legalEntityModulesRepository = _server.Services.GetRequiredService<ILegalEntityModulesRepository>();
            _invoiceRepository = _server.Services.GetRequiredService<IInvoiceRepository>();
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            _mock = new Mock<ILockManager>();
            services.AddScoped(_ => _mock.Object);
        }

        [Test()]
        public async Task StartSubmissionAsync_Multiple_STR_Submissions_Request_ShouldReturn_PreconditionFailedException()
        {
            // Arrange
            SetWorkContextUser(ClientUser);

            // Seed data for Panama legal entity
            var legalEntity = await SeedLegalEntityForPanamaAsync();

            // First call should succeed (lock acquired), second call should fail (lock not acquired)
            _mock.SetupSequence(m => m.AcquireLockAsync(It.IsAny<AcquireLockRequestDTO>()))
                 .ReturnsAsync(new LockDTO() { Id = Guid.NewGuid() })  // First call succeeds
                 .ReturnsAsync(new LockDTO() { Id = null });           // Second call fails

            // Start a submission for Panama (first call - should succeed)
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = legalEntity.Id
            };
            await _sut.StartSubmissionAsync(startSubmissionData);

            // Act - Try to start another submission (second call - should fail)
            Func<Task> act = async () => await _sut.StartSubmissionAsync(startSubmissionData);

            // Assert
            var ex = await act.Should()
               .ThrowAsync<PreconditionFailedException>();
            ex.Which.Message.Should().Contain("There is a lock on this submission");
        }

        [Test]
        public async Task GetSubmissionsWithRFICountAsync_WhenCallForClientWithRFI_ShouldReturn_NumbersOfRFI()
        {
            // Arrange
            SetWorkContextUser(ClientUser);

            // Seed data for Bahamas legal entity (belongs to _masterClient)
            var legalEntity = await SeedLegalEntityForBahamasAsync();
            await CreateAndInsertInvoiceAsync(legalEntity);

            var submittedSubmission = await CreateSubmissionWithPendingRFIAsync(legalEntity);

            // Act
            var pendingRFIsubmission = await _sut.GetSubmissionsWithRFICountAsync(_masterClient.Id, legalEntity.Id);

            // Assert
            pendingRFIsubmission.Should().Be(1, "Only one RFI should be counted for the specified master client");
        }

        [Test]
        public async Task GetUnpaidSubmissionsCountAsync_WhenCallForClientWithUnpaidSubmissions_ShouldReturn_UnpaidSubmissionCount()
        {
            // Arrange
            SetWorkContextUser(ClientUser);

            var legalEntity = await SeedLegalEntityForBahamasAsync();

            await CreateAndInsertInvoiceAsync(legalEntity);

            // Create a submission and mark as unpaid (simulate unpaid)
            var submission = await _submissionsManager.StartSubmissionAsync(new StartSubmissionDTO
            {
                ModuleId = ModuleEsId,
                LegalEntityId = legalEntity.Id
            });

            // Update submission information to set start and end dates
            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(
                submission.Id,
                new UpdateSubmissionInformationDTO
                {
                    StartAt = new DateTime(2024, 1, 1),
                    EndAt = new DateTime(2024, 12, 31)
                },
                true);

            // Submit the submission
            var submittedSubmission = await _submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO
            {
                SubmissionId = submission.Id
            }, ManagementUser.Id, ManagementUser.Email);

            // Act
            var unpaidCount = await _sut.GetUnpaidSubmissionsCountAsync(_masterClient.Id);

            // Assert
            unpaidCount.Should().Be(1, "There should be 1 unpaid submission for the master client");
        }

        private async Task<LegalEntity> SeedLegalEntityForPanamaAsync()
        {
            // Create a legal entity for Panama
            var panamaLegalEntity = new LegalEntity
            {
                MasterClientId = _masterClient.Id,
                Name = "Panama Company",
                Code = "4",
                LegacyCode = "Panama-789",
                JurisdictionId = JurisdictionPanamaId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = "345678",
                ReferralOffice = "Panama Office",
                IncorporationDate = DateTime.Now
            };
            await _legalEntitiesRepository.InsertAsync(panamaLegalEntity, true);

            var existing = new LegalEntityModule(panamaLegalEntity.Id, ModuleBfrId) { IsApproved = true, IsEnabled = true };

            await _legalEntityModulesRepository.InsertAsync(existing, saveChanges: true);

            return panamaLegalEntity;
        }

        private async Task<LegalEntity> SeedLegalEntityForBahamasAsync()
        {
            // Create a legal entity for Bahamas
            var bahamasLegalEntity = new LegalEntity
            {
                MasterClientId = _masterClient.Id,
                Name = "Bahamas Company",
                Code = "Legal entity code",
                LegacyCode = "Bahama-123",
                JurisdictionId = JurisdictionBahamasId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = "1235346567",
                ReferralOffice = "Bahamas Office",
                IncorporationDate = DateTime.Now
            };
            await _legalEntitiesRepository.InsertAsync(bahamasLegalEntity, true);

            await _legalEntityModulesRepository.InsertAsync(new LegalEntityModule(bahamasLegalEntity.Id, ModuleBfrId) { IsApproved = true, IsEnabled = true }, saveChanges: true);

            return bahamasLegalEntity;
        }

        private async Task CreateAndInsertInvoiceAsync(LegalEntity legalEntity)
        {
            // Use the existing Currency entity seeded in TestBase
            var currencyRepo = _server.Services.GetRequiredService<ICurrenciesRepository>();
            var currency = (await currencyRepo.FindByConditionAsync(c => c.Code == "USD")).Single();
            
            var document = new Document(Guid.NewGuid())
            {
                Filename = "Invoice_Document.pdf",
                Description = "Invoice related document",
                BlobPath = "path/to/blob",
                FileSize = 1024,
                AddedAt = DateTime.UtcNow
            };

            var createInvoice = new Invoice(Guid.NewGuid())
            {
                InvoiceNr = "INV-12345",
                Status = InvoiceStatus.Pending,
                LegalEntityId = legalEntity.Id,
                LegalEntity = legalEntity,
                FinancialYear = 2024,
                CurrencyId = currency.Id,
                Currency = currency,
                DocumentId = document.Id,
                Document = document,
                Date = DateTime.UtcNow,
                Layout = LayoutConsts.TridentTrust
            };

            await _invoiceRepository.InsertAsync(createInvoice, true);
        }

        private async Task<SubmissionDTO> CreateSubmissionWithPendingRFIAsync(LegalEntity legalEntity)
        {
            // Create a submission with pending RFIs for _masterClient
            var submission = await _submissionsManager.StartSubmissionAsync(new StartSubmissionDTO
            {
                ModuleId = ModuleEsId,
                LegalEntityId = legalEntity.Id
            });

            // Update submission information to set start and end dates
            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(
                submission.Id,
                new UpdateSubmissionInformationDTO
                {
                    StartAt = new DateTime(2024, 1, 1),
                    EndAt = new DateTime(2024, 12, 31)
                },
                true);

            // Submit the submission
            var submittedSubmission = await _submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO
            {
                SubmissionId = submission.Id
            }, ManagementUser.Id, ManagementUser.Email);

            // Create a request for information for the submitted submission
            await _rfiManager.CreateRequestForInformationAsync(submittedSubmission.Id, new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            });

            return submittedSubmission;
        }
    }
}