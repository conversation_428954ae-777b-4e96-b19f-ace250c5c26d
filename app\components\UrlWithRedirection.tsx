import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent, notify } from "@netpro/design-system";
import { useFetcher } from "@remix-run/react";
import { type ReactNode, useEffect, useState } from "react";
import type { MasterClientsSearchResultsDTO } from "~/services/api-generated";
import type { Company } from "~/features/companies/api/get-companies.server";
import { extractUrlParams, removeParamsFromUrl } from "~/lib/utils/url-helpers";

type ConfirmationDialogData = {
  companyId: string
  companyName: string
  masterClientId: string
  masterClientName: string
  originalUrl: string
};

type UrlWithRedirectionProps = {
  url: string
  children: ReactNode
  className?: string
  disabled?: boolean
};

export function UrlWithRedirection({ url, children, className, disabled }: UrlWithRedirectionProps): JSX.Element {
  // State for confirmation dialog
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [confirmationData, setConfirmationData] = useState<ConfirmationDialogData | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  // Fetchers for API calls
  const { data: masterClientsData, load: masterClientsLoad } = useFetcher<MasterClientsSearchResultsDTO>();
  const { data: companiesData, load: companiesLoad } = useFetcher<{ companies: Company[] }>();
  const { submit: masterClientsSubmit, data: setMasterClientData, state: masterClientsState } = useFetcher();
  const { submit: setCompanySubmit, data: setCompanyData, state: companyState } = useFetcher();

  // Handle special URL click with company/master client parameters
  function handleSpecialUrlClick(url: string, companyId: string, masterClientId: string): void {
    setIsValidating(true);

    try {
      masterClientsLoad("/api/master-clients");
      setConfirmationData({
        companyId,
        masterClientId,
        originalUrl: url,
        companyName: "",
        masterClientName: "",
      });
    } catch (error) {
      console.error("Error validating URL parameters:", error);
      notify({
        variant: "error",
        message: "An error occurred while validating the URL parameters.",
        title: "Error",
      });
    } finally {
      setIsValidating(false);
    }
  }

  // Handle confirmation of redirection
  function handleConfirmRedirection(): void {
    if (!confirmationData) {
      return;
    }

    masterClientsSubmit(
      new URLSearchParams({
        id: confirmationData.masterClientId,
        actionType: "update",
      }),
      { method: "post", action: "/api/master-clients" },
    );
  }

  // Handle click event
  function handleClick(): void {
    const urlParams = extractUrlParams(url);

    if (urlParams.setCompanyId && urlParams.setMasterClientId) {
      handleSpecialUrlClick(url, urlParams.setCompanyId, urlParams.setMasterClientId);
    } else {
      // Regular URL - open in new tab
      window.open(url, "_blank", "noopener,noreferrer");
    }
  }

  // Handle master clients validation response
  useEffect(() => {
    if (masterClientsData && confirmationData && !confirmationData.masterClientName) {
      const masterClients = masterClientsData.masterClients || [];
      const foundMasterClient = masterClients.find(
        mc => mc.masterClientId === confirmationData.masterClientId,
      );

      if (foundMasterClient) {
        companiesLoad(`/api/companies?masterClientId=${confirmationData.masterClientId}`);
        setConfirmationData(prev => prev
          ? {
              ...prev,
              masterClientName: foundMasterClient.masterClientCode || "Unknown Master Client",
            }
          : null);
      } else {
        notify({
          variant: "error",
          message: "The master client you are trying to access does not exist.",
          title: "Error",
        });
        setConfirmationData(null);
        setIsValidating(false);
      }
    }
  }, [masterClientsData, confirmationData, companiesLoad]);

  // Handle companies validation response
  useEffect(() => {
    if (companiesData && confirmationData && confirmationData.masterClientName && !confirmationData.companyName) {
      const companies = companiesData.companies || [];
      const foundCompany = companies.find(
        company => company.companyId === confirmationData.companyId,
      );

      if (foundCompany) {
        setConfirmationData(prev => prev
          ? {
              ...prev,
              companyName: foundCompany.companyName || "Unknown Company",
            }
          : null);
        setShowConfirmationDialog(true);
      } else {
        notify({
          variant: "error",
          message: "The company you are trying to access does not exist.",
          title: "Error",
        });
        setConfirmationData(null);
      }

      setIsValidating(false);
    }
  }, [companiesData, confirmationData]);

  // Handle master client setting response
  useEffect(() => {
    if (setMasterClientData && confirmationData) {
      const cleanUrl = removeParamsFromUrl(confirmationData.originalUrl, ["setCompanyId", "setMasterClientId"]);

      setCompanySubmit(
        new URLSearchParams({
          id: confirmationData.companyId,
          redirect: cleanUrl,
        }),
        { method: "post", action: "/api/companies" },
      );
    }
  }, [setMasterClientData, confirmationData, setCompanySubmit]);

  // Handle company setting response
  useEffect(() => {
    if (setCompanyData) {
      setShowConfirmationDialog(false);
      setConfirmationData(null);
    }
  }, [setCompanyData]);

  return (
    <>
      <Button
        variant="link"
        className={className}
        onClick={handleClick}
        disabled={disabled || isValidating}
      >
        {children}
      </Button>

      {/* Confirmation Dialog for Company/Master Client Switch */}
      {showConfirmationDialog && confirmationData && (
        <Dialog open={showConfirmationDialog} onOpenChange={setShowConfirmationDialog}>
          <DialogContent className="flex min-w-[500px] flex-col">
            <div className="text-lg font-semibold text-gray-800 mb-4">
              Confirm Redirection
            </div>
            <div className="text-sm text-gray-600 mb-6">
              Once confirmed, you will be redirected to company
              {" "}
              <strong>{confirmationData.companyName}</strong>
              {" "}
              from master client
              {" "}
              <strong>{confirmationData.masterClientName}</strong>
              .
            </div>
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowConfirmationDialog(false)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleConfirmRedirection}
                disabled={masterClientsState === "submitting" || companyState === "submitting"}
              >
                Confirm
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
