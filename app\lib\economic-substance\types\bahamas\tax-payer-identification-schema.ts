import { z } from "zod";
import { fileSchema } from "./file-schema";
import { nonEmptyString, preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export const currency = "USD - United States Dollar"
export const parentEntitySchema = z.object({
  name: nonEmptyString("Parent Entity Name"),
  alternativeName: z.string().optional(),
  jurisdictionOfFormation: nonEmptyString("Parent Entity's Jurisdiction of Formation "),
  incorporationNumber: nonEmptyString("Parent Entity's Incorporation Number"),
  taxpayerIdentificationNumber: nonEmptyString("Parent Entity TaxPayer Identification Number"),
})

export type ParentEntitySchemaType = z.infer<typeof parentEntitySchema>

export const taxPayerIdentificationSchema = z
  .object({
    isBahamianResident: stringBoolean(),
    files_BusinessLicense: fileSchema.optional(),
    isInvestmentFund: stringBoolean({ optional: true }),
    entityGrossTotalAnnualIncomeAmount: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true, optional: true }),
    isPartOfMneGroup: stringBoolean({ optional: true }),
    mneGroupName: nonEmptyString("Field", true),
    intendsToClaimTaxResidencyOutsideBahamas: stringBoolean({ optional: true }),
    taxResidencyJurisdiction: nonEmptyString("Field", true),
    taxPayerIdentificationNumber: nonEmptyString("Field", true),
    files_TaxPayerEvidence: fileSchema.optional(),
    hasUltimateParentEntity: stringBoolean({ optional: true }),
    ultimateParentEntities: preprocessArray(z.array(parentEntitySchema)).optional(),
    hasImmediateParentEntity: stringBoolean({ optional: true }),
    immediateParentEntities: preprocessArray(z.array(parentEntitySchema)).optional(),
  })
  .superRefine((data, ctx) => {
    // 1. Validation: If isBahamianResident is "false", isInvestmentFund is required
    if (data.isBahamianResident === "false" && !data.isInvestmentFund) {
      ctx.addIssue({
        code: "custom",
        path: ["isInvestmentFund"],
        message: "Required",
      });
    }

    if (data.isInvestmentFund !== "false") {
      return;
    }

    // 2. If isInvestmentFund is "false", validate additional rules
    if (data.isInvestmentFund === "false") {
      // 2.1. entityGrossTotalAnnualIncomeAmount is required
      if (!data.entityGrossTotalAnnualIncomeAmount) {
        ctx.addIssue({
          code: "custom",
          path: ["entityGrossTotalAnnualIncomeAmount"],
          message: "Required",
        });
      }

      // 2.2. isPartOfMneGroup is required
      if (!data.isPartOfMneGroup) {
        ctx.addIssue({
          code: "custom",
          path: ["isPartOfMneGroup"],
          message: "Required",
        });
      } else if (data.isPartOfMneGroup === "true" && !data.mneGroupName) {
        ctx.addIssue({
          code: "custom",
          path: ["mneGroupName"],
          message: "Required",
        });
      }

      if (!data.hasUltimateParentEntity) {
        ctx.addIssue({
          code: "custom",
          path: ["hasUltimateParentEntity"],
          message: "Required",
        });
      }

      if (!data.hasImmediateParentEntity) {
        ctx.addIssue({
          code: "custom",
          path: ["hasImmediateParentEntity"],
          message: "Required",
        });
      }

      if (!data.hasUltimateParentEntity
        || (data.hasUltimateParentEntity === "true" && (!data.ultimateParentEntities || data.ultimateParentEntities.length === 0))) {
        ctx.addIssue({
          code: "custom",
          path: ["ultimateParentEntities", 0],
          message: "At least one item is required",
        });
      }

      if (!data.hasImmediateParentEntity
        || (data.hasImmediateParentEntity === "true" && (!data.immediateParentEntities || data.immediateParentEntities.length === 0))) {
        ctx.addIssue({
          code: "custom",
          path: ["immediateParentEntities", 0],
          message: "At least one item is required",
        });
      }

      // 2.3. intendsToClaimTaxResidencyOutsideBahamas is required
      if (!data.intendsToClaimTaxResidencyOutsideBahamas) {
        ctx.addIssue({
          code: "custom",
          path: ["intendsToClaimTaxResidencyOutsideBahamas"],
          message: "Required",
        });
      } else if (data.intendsToClaimTaxResidencyOutsideBahamas === "true") {
        if (!data.taxResidencyJurisdiction) {
          ctx.addIssue({
            code: "custom",
            path: ["taxResidencyJurisdiction"],
            message: "Required.",
          });
        }

        if (!data.taxPayerIdentificationNumber) {
          ctx.addIssue({
            code: "custom",
            path: ["taxPayerIdentificationNumber"],
            message: "Required.",
          });
        }

        if (!data.files_TaxPayerEvidence) {
          ctx.addIssue({
            code: "custom",
            path: ["files_TaxPayerEvidence"],
            message: "Required.",
          });
        }
      }
    }
  });

export type TaxPayerIdentificationSchemaType = z.infer<typeof taxPayerIdentificationSchema>;
