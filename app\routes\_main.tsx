import type { LoaderFunctionArgs } from "@remix-run/node";
import { Outlet, json, useLoaderData } from "@remix-run/react";
import type { JSX } from "react";
import { Sidebar } from "~/components/layout/sidebar/Sidebar";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { FeatureFlagProvider } from "~/lib/feature-flag/context/feature-flags-context";
import { middleware } from "~/lib/middlewares.server";
import { commonGetFeatureFlags, getInboxInfo } from "~/services/api-generated";

export async function loader({ request }: LoaderFunctionArgs) {
  const { userId } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { data: inboxData, error: inboxError } = await getInboxInfo({
    headers: await authHeaders(request),
    query: { userId },
  });
  const { data: featureFlags } = await commonGetFeatureFlags({
    headers: await authHeaders(request),
  });

  if (inboxError || !inboxData) {
    throw new Response("Failed to fetch inbox info", { status: 500 });
  }

  if (!featureFlags) {
    throw new Response("Failed to fetch feature flags", { status: 500 });
  }

  return json({
    inboxData,
    featureFlags,
  });
}

export default function MainLayout(): JSX.Element {
  const { featureFlags } = useLoaderData<typeof loader>();

  return (
    <FeatureFlagProvider initialFlags={{ featureFlags: featureFlags || [] }}>
      <Sidebar>
        <Outlet />
      </Sidebar>
    </FeatureFlagProvider>
  );
}
