﻿// <copyright file="MasterClientProfileTests.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using FluentAssertions;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.DataManager.AutoMapper;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AutoMapper
{
    [TestFixture()]
    public class MasterClientProfileTests : TestBase
    {
        private IMapper _mapper;

        [SetUp]
        public Task SetUp()
        {
            SetupBase().GetAwaiter().GetResult(); // Ensures database seeding is complete

            _mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<ApplicationProfile>();
                cfg.AddProfile<MasterClientProfile>();
            }).CreateMapper();

            return Task.CompletedTask;
        }

        [Test]
        public void Map_ForMasterClientWithInactiveLegalEntity_SetsHasActiveCompaniesToFalse()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC123",
            };

            var company = new LegalEntity(Guid.NewGuid())
            {
                Name = "Test Company",
                Code = "TC123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };

            // Approve the company onboarding
            company.SetInactive();

            masterClient.LegalEntities.Add(company);

            // Act 
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveCompanies.Should().BeFalse();
        }

        [Test]
        public void Map_WhenLegalEntityHasOnboardingStatusOnboarding_SetsHasActiveCompaniesToFalse()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC123",
            };

            var company = new LegalEntity
            {
                Name = "Test Company",
                Code = "TC123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };

            // Set the company to inactive
            company.SetInactive();

            // Approve the company onboarding
            company.ResetToOnboarding();

            masterClient.LegalEntities.Add(company);

            // Act 
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveCompanies.Should().BeFalse();
        }

        [Test]
        public void Map_WhenLegalEntityHasOnboardingStatusClosedWhileOnboarding_SetsHasActiveCompaniesToFalse()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC123",
            };

            var company = new LegalEntity
            {
                Name = "Test Company",
                Code = "TC123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };

            //// Set the company to inactive
            company.SetInactive();

            // Approve the company onboarding
            company.CloseWhileOnboarding();

            masterClient.LegalEntities.Add(company);

            // Act 
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveCompanies.Should().BeFalse();
        }

        [Test]
        public void Map_WhenLegalEntityHasOnboardingStatusDeclined_SetsHasActiveCompaniesToFalse()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC123",
            };

            var company = new LegalEntity
            {
                Name = "Test Company",
                Code = "TC123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };

            // Approve the company onboarding
            company.DeclineOnboarding();

            masterClient.LegalEntities.Add(company);

            // Act 
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveCompanies.Should().BeFalse();
        }

        [Test]
        public void Map_WhenLegalEntityHasOnboardingStatusApproved_SetsHasActiveCompaniesToTrue()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC123",
            };

            var company = new LegalEntity
            {
                Name = "Test Company",
                Code = "TC123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };

            // Approve the company onboarding
            company.ApproveOnboarding();

            masterClient.LegalEntities.Add(company);

            // Act 
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveCompanies.Should().BeTrue();
        }

        [Test]
        public void Map_ForMasterClientWithMultipleCompanies_AtLeastOneActiveAndOnboardingStatusApproved_SetsHasActiveCompaniesToTrue()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC456",
            };

            var activeApprovedCompany = new LegalEntity
            {
                Name = "Active Approved Company",
                Code = "AAC123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };
            activeApprovedCompany.ApproveOnboarding();

            var inactiveCompany = new LegalEntity
            {
                Name = "Inactive Company",
                Code = "IC123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };
            inactiveCompany.SetInactive();

            masterClient.LegalEntities.Add(activeApprovedCompany);
            masterClient.LegalEntities.Add(inactiveCompany);

            // Act
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveCompanies.Should().BeTrue();
        }

        [Test]
        public void Map_ForMasterClientWithActiveRequestsForInformation_SetsHasActiveRequestsForInformationToTrue()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC789",
            };

            var company = new LegalEntity
            {
                Name = "Company With RFI",
                Code = "RFI123",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };

            // Approve the company onboarding
            company.ApproveOnboarding();

            // Simulate active RequestForInformation
            var submission = new Submission(Guid.NewGuid())
            {
                LegalEntityId = company.Id,
                Status = SubmissionStatus.InformationRequested,
                CreatedBy = Guid.NewGuid(),
            };

            var requestForInformation = new RequestForInformation()
            {
                SubmissionId = submission.Id,
                DeadLine = DateTime.UtcNow.AddDays(7),
                Comments = "Please provide the requested documents.",
                Status = RequestForInformationStatus.Active,
                CreatedBy = Guid.NewGuid(),
            };

            submission.RequestsForInformation.Add(requestForInformation);

            // Add the submission to the company's Submissions collection
            company.Submissions.Add(submission);

            masterClient.LegalEntities.Add(company);

            // Act
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveRequestsForInformation.Should().BeTrue();
        }

        [Test]
        public void Map_ForMasterClientWithNoActiveRequestsForInformation_SetsHasActiveRequestsForInformationToFalse()
        {
            // Arrange
            var masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "MC790",
            };

            var company = new LegalEntity
            {
                Name = "Company Without RFI",
                Code = "RFI124",
                EntityType = LegalEntityType.Company,
                MasterClientId = masterClient.Id,
            };
            company.ApproveOnboarding();
           
            var submission = new Submission(Guid.NewGuid())
            {
                LegalEntityId = company.Id,
                Status = SubmissionStatus.Revision,
                CreatedBy = Guid.NewGuid(),
            };

            var requestForInformation = new RequestForInformation()
            {
                SubmissionId = submission.Id,
                DeadLine = DateTime.UtcNow.AddDays(7),
                Comments = "Please provide the requested documents.",
                Status = RequestForInformationStatus.Completed,
                CreatedBy = Guid.NewGuid(),
            };

            submission.RequestsForInformation.Add(requestForInformation);

            // Add the submission to the company's Submissions collection
            company.Submissions.Add(submission);

            masterClient.LegalEntities.Add(company);

            // Act
            var result = _mapper.Map<MasterClientsSearchResultDTO>(masterClient);

            // Assert
            result.HasActiveRequestsForInformation.Should().BeFalse();
        }


    }
}