// <copyright file="IInvoiceAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Counters;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Payments.Invoices
{
    /// <summary>
    /// Interface defining the contract for invoice-related services.
    /// </summary>
    public interface IInvoiceAppService : IScopedService
    {
        /// <summary>
        /// Retrieves a paged list of invoices based on the provided request parameters.
        /// </summary>
        /// <param name="request">The request containing filters and pagination parameters.</param>
        /// <returns>A task that represents the asynchronous operation.
        /// The task result contains a paged list of invoices.</returns>
        Task<IPagedList<InvoiceDTO>> ListInvoicesAsync(InvoiceListRequestDTO request);

        /// <summary>
        /// Retrieves an invoice by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the invoice to retrieve.</param>
        /// <returns>A task that represents the asynchronous operation.
        /// The task result contains the invoice details.</returns>
        Task<InvoiceDTO> GetInvoiceByIdAsync(Guid id);

        /// <summary>
        /// Gets the number of unpaid invoices.
        /// </summary>
        /// <param name="masterClientId">The ID of the master client to check for pending payment submissions.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the count of unpaid invoices.</returns>
        Task<InvoiceInfoDTO> GetUnpaidInvoicesCountAsync(Guid masterClientId);
    }
}