﻿// <copyright file="OverviewController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Counters;
using NetProGroup.Trust.Application.Contracts.Inboxes;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using NetProGroup.Trust.Application.Contracts.Submissions;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Overview
{
    /// <summary>
    /// Controller for handling client dashboard tags operations.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class OverviewController : TrustAPIControllerBase
    {
        private readonly IInboxAppService _inboxAppService;
        private readonly ISubmissionsAppService _submissionAppService;
        private readonly IInvoiceAppService _invoiceAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="OverviewController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="submissionsAppService">The submissions application service.</param>
        /// <param name="inboxAppService">The inbox application service.</param>
        /// <param name="invoiceAppService">The invoice application service.</param>
        public OverviewController(
           ILogger<OverviewController> logger,
           ISubmissionsAppService submissionsAppService,
           IInboxAppService inboxAppService,
           IInvoiceAppService invoiceAppService) : base(logger)
        {
            _inboxAppService = inboxAppService;
            _submissionAppService = submissionsAppService;
            _invoiceAppService = invoiceAppService;
        }

        /// <summary>
        /// Gets the counter tags for the client dashboard, including pending payment count, RFI status, and pending inbox messages count.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1.0/client/tags/counters?masterClientId=00000000-0000-0000-0000-000000000000.
        ///
        /// </remarks>
        /// <param name="masterClientId">The unique identifier of the master client.</param>
        /// <param name="companyId">The unique identifier of the company.</param>
        /// <returns>
        /// A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.
        /// </returns>
        [HttpGet("counters")]
        [SwaggerOperation(
            OperationId = "Client_GetCounterTags",
            Summary = "Get counter tags for the client dashboard",
            Description = "Returns pending payment count, RFI status, and pending inbox messages count for the specified master client and optionally filtered by company.")]
        [ProducesResponseType(typeof(CountersDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCounterTags(
            [FromQuery, Required] Guid masterClientId,
            [FromQuery, Required] Guid companyId) {
            CountersDTO item = null;
            var result = await ProcessRequestAsync<CountersDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(masterClientId, nameof(masterClientId));
                },
                executeAsync: async () =>
                {
                    item = new CountersDTO
                    {
                        InvoiceCounter = await _invoiceAppService.GetUnpaidInvoicesCountAsync(masterClientId),
                        PendingRFIsCounter = await _submissionAppService.GetSubmissionsWithRFICountAsync(masterClientId, companyId),
                        InboxCounter = await _inboxAppService.GetInboxInfoAsync()
                    };
                },
                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
