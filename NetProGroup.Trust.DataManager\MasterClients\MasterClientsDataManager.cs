﻿// <copyright file="MasterClientsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.Configuration;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Invitations;
using NetProGroup.Trust.DataManager.LegalEntityRelations.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.DataManager.Users.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Scheduling;
using NetProGroup.Trust.Domain.Shared.Constants;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Domain.Sync;
using NetProGroup.Trust.Domain.Users;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.MasterClients
{
    /// <summary>
    /// Manager for MasterClient data.
    /// </summary>
    public class MasterClientsDataManager : IMasterClientsDataManager
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly TrustDbContext _trustDbContext;

        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly IMasterClientUsersRepository _masterClientUsersRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly IUserRepository _usersRepository;
        private readonly IApplicationUsersRepository _applicationUsersRepository;
        private readonly IUsersDataManager _usersDataManager;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IUserInvitationsDataManager _userInvitationsDataManager;
        private readonly IConfigurationManager _configurationManager;

        private readonly ILockManager _lockManager;
        private readonly IBulkOperationProvider _bulkOperationProvider;

        private readonly TrustOfficeOptions _trustOfficeOptions;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;
        private readonly GoLiveOptions _goLiveOptions;
        private LockDTO _jobLock;

        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientsDataManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="trustDbContext">Instance of the current DbContext.</param>
        /// <param name="masterClientsRepository">Instance of the MasterClients repository.</param>
        /// <param name="masterClientUsersRepository">Instance of the MasterClientUsers repository.</param>
        /// <param name="jurisdictionRepository">Instance of the Jurisdictions repository.</param>
        /// <param name="submissionsRepository">Instance of submission repository.</param>
        /// <param name="usersRepository">Instance of the ApplicationUsers repository.</param>
        /// <param name="usersDataManager">Instance of the UsersDataManager.</param>
        /// <param name="systemAuditManager">Instance of the SystemAuditManager.</param>
        /// <param name="userInvitationsDataManager">Instance of user invitations data manager.</param>
        /// <param name="trustOfficeOptions">Configuration for TrustOffice.</param>
        /// <param name="lockManager">Instance of lock manager.</param>
        /// <param name="authorizationFilterExpressionFactory">Factory for creating authorization filters.</param>
        /// <param name="configurationManager">Manager for configurations.</param>
        /// <param name="goLiveOptions">Options for GoLive.</param>
        /// <param name="bulkOperationProvider">Provider for bulk operations.</param>
        /// <param name="applicationUsersRepository">The instance of the ApplicationUsers repository.</param>
        public MasterClientsDataManager(
            ILogger<MasterClientsDataManager> logger,
            IMapper mapper,
            IWorkContext workContext,
            TrustDbContext trustDbContext,
            IMasterClientsRepository masterClientsRepository,
            IMasterClientUsersRepository masterClientUsersRepository,
            IJurisdictionsRepository jurisdictionRepository,
            ISubmissionsRepository submissionsRepository,
            IUserRepository usersRepository,
            IUsersDataManager usersDataManager,
            ISystemAuditManager systemAuditManager,
            IUserInvitationsDataManager userInvitationsDataManager,
            IOptions<TrustOfficeOptions> trustOfficeOptions,
            ILockManager lockManager,
            IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory,
            IConfigurationManager configurationManager,
            IOptions<GoLiveOptions> goLiveOptions,
            IBulkOperationProvider bulkOperationProvider,
            IApplicationUsersRepository applicationUsersRepository)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _trustDbContext = trustDbContext;

            _masterClientsRepository = masterClientsRepository;
            _masterClientUsersRepository = masterClientUsersRepository;
            _jurisdictionsRepository = jurisdictionRepository;
            _submissionsRepository = submissionsRepository;
            _usersRepository = usersRepository;
            _usersDataManager = usersDataManager;
            _systemAuditManager = systemAuditManager;
            _userInvitationsDataManager = userInvitationsDataManager;
            _lockManager = lockManager;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
            _configurationManager = configurationManager;
            _applicationUsersRepository = applicationUsersRepository;
            _bulkOperationProvider = Check.NotNull(bulkOperationProvider, nameof(bulkOperationProvider));

            ArgumentNullException.ThrowIfNull(trustOfficeOptions, nameof(trustOfficeOptions));
            _trustOfficeOptions = trustOfficeOptions.Value;

            ArgumentNullException.ThrowIfNull(goLiveOptions, nameof(goLiveOptions));
            _goLiveOptions = goLiveOptions.Value;
        }

        /// <summary>
        /// Creates the MasterClient from the model.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <param name="saveChanges">Whether to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<MasterClientDTO> CreateMasterClientAsync(CreateMasterClientDTO model, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(model);

            #region Setup entity

            var entity = new MasterClient
            {
                Code = model.Code
            };

            #endregion

            await _masterClientsRepository.InsertAsync(entity, saveChanges);

            return _mapper.Map<MasterClientDTO>(entity);
        }

        /// <summary>
        /// Updates the MasterClient from the model.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <param name="saveChanges">Whether to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<MasterClientDTO> UpdateMasterClientAsync(UpdateMasterClientDTO model, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(model);

            #region Get the masterclient

            var entity = await _masterClientsRepository.GetByIdAsync(model.Id);

            entity.Code = model.Code;

            #endregion

            return _mapper.Map<MasterClientDTO>(entity);
        }

        /// <inheritdoc/>
        public async Task<MasterClientDTO> GetMasterClientAsync(Guid masterclientId)
        {
            var masterClient = await _masterClientsRepository.GetByIdAsync(masterclientId,
                options: q => q.Include(mc => mc.MasterClientUsers)
                               .Include(mc => mc.LegalEntities).ThenInclude(le => le.Jurisdiction));

            if (masterClient == null)
            {
                throw new NotFoundException(ApplicationErrors.MASTERCLIENT_NOT_FOUND.ToErrorCode(), $"The masterclient with id '{masterclientId}' was not found");
            }

            var masterClientDTO = _mapper.Map<MasterClientDTO>(masterClient);

            var usersByMasterClient = await _usersDataManager.ListMasterClientUsersByMasterClientAsync(
                new ListUsersByMasterClientRequest()
                {
                    MasterClientIds = new Guid[] { masterclientId },
                    PagingInfo = new PagingInfo() { PageNumber = 1, PageSize = Int32.MaxValue }
                });

            var userList = (usersByMasterClient
                    .SingleOrDefault(u => u.MasterClientId == masterClient.Id)
                    .MasterClientUserItems ?? new List<ListUserDTO>()).ToList();

            var uniqueUserList = new Dictionary<Guid, ListUserDTO>();
            foreach (var user in userList)
            {
                uniqueUserList.TryAdd(user.Id, user);
            }

            var ownerUserIds = masterClient.MasterClientUsers.Where(mcu => !mcu.IsManuallyAdded).Select(mcu => mcu.UserId).Distinct();
            var managerUserIds = masterClient.MasterClientUsers.Where(mcu => mcu.IsManuallyAdded).Select(mcu => mcu.UserId).Distinct();

            masterClientDTO.MasterClientUsers = uniqueUserList.Values.Where(x => ownerUserIds.Contains(x.Id)).ToArray();
            masterClientDTO.MasterClientManagers = uniqueUserList.Values.Where(x => managerUserIds.Contains(x.Id)).ToArray();

            return masterClientDTO;
        }

        /// <summary>
        /// Imports the MasterClient and creates / links users by email address.
        /// </summary>
        /// <param name="request">The request with all parameters.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task ImportMasterClients(CreateMasterClientsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request);

            // Start a transaction for 'all or nothing'
            using var transaction = await _masterClientsRepository.DbContext.Database.BeginTransactionAsync();
            {
                try
                {
                    var jurisdictions = new Dictionary<Guid, Jurisdiction>();
                    var masterClients = new Dictionary<string, MasterClient>(StringComparer.OrdinalIgnoreCase);

                    foreach (var masterClientData in request.MasterClients)
                    {
                        // Get jurisdiction first
                        Jurisdiction jurisdiction;

                        if (!jurisdictions.TryGetValue(masterClientData.JurisdictionId, out jurisdiction))
                        {
                            jurisdiction = await _jurisdictionsRepository.GetByIdAsync(masterClientData.JurisdictionId);
                            if (jurisdiction == null)
                            {
                                throw new BadRequestException(ApplicationErrors.JURISDICTION_NOT_FOUND.ToErrorCode(), $"Jurisdiction '{masterClientData.JurisdictionId}' not found");
                            }

                            jurisdictions.Add(masterClientData.JurisdictionId, jurisdiction);
                        }

                        // Get existing masterclient
                        if (!masterClients.TryGetValue(masterClientData.MasterClientCode, out MasterClient masterClient))
                        {
                            masterClient = await _masterClientsRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == masterClientData.MasterClientCode,
                                                                                                                  q => q.Include(mc => mc.MasterClientUsers).ThenInclude(mcu => mcu.User));
                            if (masterClient == null)
                            {
                                // Create the MasterClient
                                masterClient = new MasterClient
                                {
                                    Code = masterClientData.MasterClientCode
                                };
                                jurisdiction.MasterClients.Add(masterClient);
                            }

                            masterClients.Add(masterClientData.MasterClientCode, masterClient);
                        }

                        var masterClientUsers = new List<MasterClientUser>();

                        foreach (var emailAddress in masterClientData.EmailAddresses)
                        {
                            var masterClientUser = masterClient.MasterClientUsers.FirstOrDefault(x => x.User.Email != null && x.User.Email.Equals(emailAddress, StringComparison.OrdinalIgnoreCase));

                            // Only create if not exists for the MasterClient yet.
                            if (masterClientUser == null)
                            {
                                // Gets existing user or create one now
                                var user = await GetOrCreateClientUserFromEmailAsync(emailAddress, true);
                                masterClientUser = new MasterClientUser
                                {
                                    IsManuallyAdded = false,
                                    UserId = user.Id
                                };
                                masterClientUsers.Add(new MasterClientUser { UserId = user.Id });
                            }
                        }

                        if (masterClientData.DeleteNonExistingEmailAddresses)
                        {
                            // Remove the users from the MasterClient that are not listed.
                            masterClientUsers.RemoveAll(x => !masterClientData.EmailAddresses.Contains(x.User.Email));
                        }

                        // Add the new users
                        masterClientUsers.ForEach(x => masterClient.MasterClientUsers.Add(x));
                    }

                    await _masterClientsRepository.DbContext.SaveChangesAsync();

                    await transaction.CommitAsync();
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }

        /// <summary>
        /// Syncs the MasterClient and creates / links users by email address.
        /// </summary>
        /// <param name="request">The request with all parameters.</param>
        /// <param name="jobLock">Optional job lock to prevent concurrent execution.</param>
        /// <param name="beforeCommitAsync">Optional callback function to execute before committing database changes.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SyncMasterClientsAsync(SyncMasterClientRequest request, LockDTO jobLock = null, Func<DbContext, Task> beforeCommitAsync = null)
        {
            ArgumentNullException.ThrowIfNull(request);

            _logger.LogInformation("Found {Count} masterclient users", request.MasterClientUsers.Count);

            _jobLock = jobLock;

            var sw = new Stopwatch();
            sw.Start();

            var count = 0;

            // Get the configured Go Live date
            // If null, there is no go live planned
            var goLiveDate = _goLiveOptions.GoLiveDate;
            var isGoLiveDay = goLiveDate.Date == DateTime.Today;

            var masterClientUsersWithEmail = request.MasterClientUsers.Where(mcu => !string.IsNullOrEmpty(mcu.UserEmail)).ToList();

            // Create all masterclients
            await CreateOrUpdateMasterClientsAsync(masterClientUsersWithEmail);

            // Create all users
            await CreateOrUpdateUsersAsync(masterClientUsersWithEmail);

            // Load all masterclients with users
            var masterClients = (await _masterClientsRepository.FindAllAsync(q => q.Include(mc => mc.MasterClientUsers).ThenInclude(mcu => mcu.User))).ToDictionary(mc => mc.Code, StringComparer.OrdinalIgnoreCase);

            // Load all users
            var users = new Dictionary<string, ApplicationUser>(StringComparer.OrdinalIgnoreCase);
            var usersFromDb = await _usersRepository.SearchAsync();
            foreach (var user in usersFromDb)
            {
                if (user.ApplicationUserRoles.Any(aur => aur.RoleId == WellKnownRoleIds.Client))
                {
                    if (!users.TryAdd(user.Email, user))
                    {
                        // ToDo: report duplicate
                    }
                }
            }

            var masterClientUsersToInsert = new List<MasterClientUser>();
            var masterClientUsersToDelete = new List<MasterClientUser>();
            var activityLogsToInsert = new List<ActivityLog>();
            var invitationsToInsert = new List<QueuedJob>();
            var syncMessagesToInsert = new List<SyncMessage>();

            var masterClienUserHashSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            foreach (var syncMasterClientUser in masterClientUsersWithEmail)
            {
                count++;

                if (_jobLock != null && sw.Elapsed.TotalSeconds > 60)
                {
                    await _lockManager.RefreshLockAsync(_jobLock.Id.Value);
                    sw.Restart();
                }

                var key = $"{syncMasterClientUser.MasterClientCode}/{syncMasterClientUser.UserEmail}";
                if (masterClienUserHashSet.Add(key))
                {
                    var masterClient = masterClients[syncMasterClientUser.MasterClientCode];
                    var emailAddress = syncMasterClientUser.UserEmail;

                    if (!string.IsNullOrEmpty(emailAddress))
                    {
                        if (!masterClient.MasterClientUsers.Any(mcu => mcu.IsManuallyAdded == false && mcu.User.Email != null && mcu.User.Email.Equals(emailAddress, StringComparison.OrdinalIgnoreCase)))
                        {
                            using (var scope = _logger.BeginScope(new { syncMasterClientUser.MasterClientCode }))
                            {
                                if (!users.TryGetValue(emailAddress, out var user))
                                {
                                    _logger.LogError("Sync is unable to find user '{Email}'", emailAddress);

                                    // ToDO Report failure
                                }
                                else
                                {
                                    _logger.LogInformation("Sync is assigning user '{Email}' to masterclient '{Code}'", user.Email, syncMasterClientUser.MasterClientCode);

                                    var activityLogs = await _systemAuditManager.CreateUserAddedToMasterClientActivityLogAsync(user, masterClient);
                                    activityLogsToInsert.AddRange(activityLogs);

                                    masterClientUsersToInsert.Add(new MasterClientUser
                                    {
                                        IsManuallyAdded = false,
                                        MasterClientId = masterClient.Id,
                                        UserId = user.Id,
                                    });

                                    var isReregistration = isGoLiveDay;
                                    invitationsToInsert.Add(_userInvitationsDataManager.CreateInvitationToUserJob(user.Id, reregistration: isReregistration));
                                }
                            }
                        }
                    }
                }
            }

            // Now check all master client users to see if the emailaddress is still in use
            foreach (var masterClient in masterClients.Values)
            {
                foreach (var masterClientUser in masterClient.MasterClientUsers)
                {
                    var emailAddress = masterClientUser.User.Email;
                    var key = $"{masterClient.Code}/{emailAddress}";

                    if (!masterClienUserHashSet.Contains(key))
                    {
                        var existingMasterClientUser = masterClient.MasterClientUsers.FirstOrDefault(mcu => mcu.User.Email != null && mcu.User.Email.Equals(emailAddress, StringComparison.OrdinalIgnoreCase));

                        if (existingMasterClientUser != null && !existingMasterClientUser.IsManuallyAdded)
                        {
                            _logger.LogDebug("Sync is removing user '{Email}' from masterclient '{Code}'", emailAddress, masterClient.Code);

                            var activityLogs = await _systemAuditManager.CreateUserRemovedFromMasterClientActivityLogAsync(existingMasterClientUser.User, masterClient);
                            activityLogsToInsert.AddRange(activityLogs);

                            masterClientUsersToDelete.Add(existingMasterClientUser);
                        }
                    }
                }
            }

            using var transaction = await _masterClientsRepository.DbContext.Database.BeginTransactionAsync();

            try
            {
                if (_masterClientsRepository.DbContext.Database.IsRelational())
                {
                    _masterClientsRepository.DbContext.Database.SetCommandTimeout(300);
                }

                _logger.LogInformation("Start saving masterclientuser data (in a Tx)...");

                if (masterClientUsersToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} master client users...", masterClientUsersToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(masterClientUsersToInsert, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No master client users to insert");
                }

                if (masterClientUsersToDelete.Count > 0)
                {
                    _logger.LogInformation("Starting BulkDelete for {Count} masterclients...", masterClientUsersToDelete.Count);
                    await _bulkOperationProvider.BulkDeleteAsync(masterClientUsersToDelete, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkDelete finished");
                }
                else
                {
                    _logger.LogInformation("No master client users to delete");
                }

                if (invitationsToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} invitations...", invitationsToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(invitationsToInsert, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No invitations to insert");
                }

                if (activityLogsToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} activitylogs...", activityLogsToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(activityLogsToInsert, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No activity logs to insert");
                }

                if (syncMessagesToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} messages...", syncMessagesToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(syncMessagesToInsert, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No messages to insert");
                }

                if (beforeCommitAsync != null)
                {
                    await beforeCommitAsync(_masterClientsRepository.DbContext);
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Done saving data");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error saving data (Tx rolled back)");
                throw;
            }
        }

        /// <summary>
        /// Checks if the email/masterclientcode relation exists.
        /// </summary>
        /// <param name="masterClientCode">The MasterClientCode to check for.</param>
        /// <param name="email">The emailaddress to check for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> EmailExistsForMasterClientCodeAsync(string masterClientCode, string email)
        {
            var exists = await _masterClientsRepository.AnyByConditionAsync(mc => mc.Code == masterClientCode && mc.MasterClientUsers.Any(mcu => mcu.User.Email == email));

            return exists;
        }

        /// <inheritdoc/>
        public async Task<ListMasterClientsResponse> ListMasterClientsAsync(ListMasterClientsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var result = new ListMasterClientsResponse();

            var searchTerm = request.SearchTerm;
            var jurisdictionId = request.JurisdictionId;

            Expression<Func<MasterClient, bool>> predicate = mc => true;
            if (jurisdictionId != null)
            {
                predicate = predicate.And(mc => mc.LegalEntities.Any(entity => entity.JurisdictionId == jurisdictionId));
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
#pragma warning disable CA1307 // Specify StringComparison for clarity
                predicate = predicate.And(mc =>
                    mc.Code.Contains(searchTerm) ||
                    mc.MasterClientUsers.Any(user => user.User.Email.Contains(searchTerm)));
#pragma warning restore CA1307 // Specify StringComparison for clarity
            }

            var pagedData = await _masterClientsRepository.FindByConditionAsPagedListAsync(predicate,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                options: q => q.OrderBy(mc => mc.Code)
                               .Include(mc => mc.LegalEntities)
                               .Include(c => c.MasterClientUsers).ThenInclude(mcu => mcu.User)
                               .TagWithCallSite());

            var subset = _mapper.Map<List<MasterClientDTO>>(pagedData);

            var usersByMasterClient = await _usersDataManager.ListMasterClientUsersByMasterClientAsync(
                new ListUsersByMasterClientRequest()
                {
                    MasterClientIds = subset.Select(client => client.Id).ToList(),
                    PagingInfo = new PagingInfo() { PageNumber = 1, PageSize = Int32.MaxValue }
                });

            foreach (var masterClient in subset)
            {
                var userList = usersByMasterClient
                    .SingleOrDefault(u => u.MasterClientId == masterClient.Id)
                    .MasterClientUserItems ?? new List<ListUserDTO>();

                var uniqueUserList = new Dictionary<Guid, ListUserDTO>();
                foreach (var user in userList)
                {
                    uniqueUserList.TryAdd(user.Id, user);
                }

                var masterClientFromDb = pagedData.Single(x => x.Id == masterClient.Id);

                var ownerUserIds = masterClientFromDb.MasterClientUsers.Where(mcu => !mcu.IsManuallyAdded).Select(mcu => mcu.UserId).Distinct();
                var managerUserIds = masterClientFromDb.MasterClientUsers.Where(mcu => mcu.IsManuallyAdded).Select(mcu => mcu.UserId).Distinct();

                masterClient.MasterClientUsers = uniqueUserList.Values.Where(x => ownerUserIds.Contains(x.Id)).ToArray();
                masterClient.MasterClientManagers = uniqueUserList.Values.Where(x => managerUserIds.Contains(x.Id)).ToArray();
            }

            result.MasterClientItems = new StaticPagedList<MasterClientDTO>(subset, pagedData.GetMetaData());

            return result;
        }

        /// <inheritdoc/>
        public async Task<SearchMasterClientsResponse> SearchMasterClientsAsync(SearchMasterClientsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var result = new SearchMasterClientsResponse();

            // Code contains searchterm OR Jurisdiction contains searchterm
#pragma warning disable CA1307 // Specify StringComparison for clarity
            Expression<Func<MasterClient, bool>> predicate = mc =>
                request.SearchTerm == string.Empty
                || request.SearchTerm == null
                || mc.Code.Contains(request.SearchTerm)
                || mc.LegalEntities.Any(le => le.Jurisdiction.Name.Contains(request.SearchTerm));
#pragma warning restore CA1307 // Specify StringComparison for clarity

            // AND User has access to MasterClient
            predicate = predicate.And(mc => mc.MasterClientUsers.Any(mcu => mcu.UserId == request.UserId));

            var data = await _masterClientsRepository.FindByConditionMappedAsync<MasterClient, MasterClientsSearchResultDTO>(predicate,
                _mapper.ConfigurationProvider,
                options: q => q.Include(mc => mc.LegalEntities).ThenInclude(le => le.Jurisdiction)
                      .OrderBy(x => x.Code));

            foreach (var item in data)
            {
                result.MasterClientItems.Add(item);
            }

            return result;
        }

        /// <summary>
        /// Manually adds a user to a master client.
        /// </summary>
        /// <param name="model">The data transfer object containing the user and master client information.</param>
        /// <param name="saveChanges">Denotes wehther to save the changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<MasterClientUserDTO> CreateUserToMasterClientAsync(CreateMasterClientUserDTO model, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            Check.NotNullOrWhiteSpace(model.EmailAddress, nameof(model.EmailAddress));
            Check.NotDefaultOrNull<Guid>(model.MasterClientId, nameof(model.MasterClientId));

            #region Sanity check

            var masterClient = await _masterClientsRepository.CheckMasterClientByIdAsync(model.MasterClientId);

            // Check if the email address is for the correct domain
            var emailAddress = model.EmailAddress;
            var allowedDomains = _trustOfficeOptions.AllowedDomains.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(x => x.ToLower()).ToArray();
            if (!allowedDomains.Any(domain => emailAddress.EndsWith($"@{domain}", StringComparison.OrdinalIgnoreCase)))
            {
                throw new BadRequestException($"Email address should end with one of {allowedDomains.Aggregate((s, s1) => $"{s}, {s1}")}");
            }

            #endregion

            masterClient = await _masterClientsRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == masterClient.Code,
                                                                                             q => q.Include(mc => mc.MasterClientUsers).ThenInclude(mcu => mcu.User));

            // Get or create the user
            var user = await GetOrCreateClientUserFromEmailAsync(emailAddress, false);

            MasterClientUser masterClientUser = null;

            // Create user if not exists yet
            if (!masterClient.MasterClientUsers.Any(mcu => mcu.IsManuallyAdded && mcu.User.Email != null && mcu.User.Email.Equals(emailAddress, StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogInformation("Assigning user '{Email}' to masterclient '{Code}'", emailAddress, masterClient.Code);

                await _systemAuditManager.AddUserAddedToMasterClientActivityLogAsync(user, masterClient, saveChanges: false);

                masterClientUser = new MasterClientUser
                {
                    User = user,
                    IsManuallyAdded = true,
                };
                masterClient.MasterClientUsers.Add(masterClientUser);
            }
            else
            {
                throw new ConflictException(ApplicationErrors.MASTERCLIENT_USER_EXISTS.ToErrorCode(), $"Manager already exists");
            }

            // If user already has an objectid, it is a reregistration (however an invitation is not needed as the user already has an account)
            bool reregistration = user.ObjectId != null;

            await _userInvitationsDataManager.SendInvitationToUserAsync(user.Id, masterClientCode: masterClient.Code, reregistration: reregistration, saveChanges: false);

            await _masterClientsRepository.SaveChangesAsync();

            return _mapper.Map<MasterClientUserDTO>(masterClientUser);
        }

        /// <inheritdoc/>
        public async Task RemoveUserFromMasterClientAsync(RemoveMasterClientUserDTO model, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(model);

            #region Sanity check

            var masterClient = await _masterClientsRepository.CheckMasterClientByIdAsync(model.MasterClientId);
            var user = await _usersRepository.CheckUserByIdAsync(model.UserId);

            var masterClientUser = await _masterClientUsersRepository.FindFirstOrDefaultByConditionAsync(mcu => mcu.MasterClientId == model.MasterClientId &&
                                                                                                                mcu.UserId == model.UserId);
            if (masterClientUser == null)
            {
                return;
            }

            if (!masterClientUser.IsManuallyAdded)
            {
                throw new BadRequestException(ApplicationErrors.MASTERCLIENT_USER_IS_NOT_MANUALLY_ADDED.ToErrorCode(), "Can not remove a user that was not manually added.");
            }

            #endregion

            await _systemAuditManager.AddUserRemovedFromMasterClientActivityLogAsync(user, masterClient, saveChanges: false);
            await _masterClientUsersRepository.DeleteAsync(masterClientUser, saveChanges);
        }

        /// <summary>
        /// Gets the user by email.
        /// </summary>
        /// <param name="email">The email address to search for.</param>
        /// <param name="fromSync">Denotes whether this is called from the sync or not (for logging).</param>
        /// <returns><see cref="Task"/> representing the asynchronous operation.</returns>
        protected virtual async Task<ApplicationUser> GetOrCreateClientUserFromEmailAsync([NotNull] string email, bool fromSync)
        {
            var user = await _usersDataManager.GetClientUserByEmailAsync(email);

            if (user == null)
            {
                if (fromSync)
                {
                    _logger.LogInformation("Sync is creating new user '{Email}'", email);
                }
                else
                {
                    _logger.LogInformation("Creating new user '{Email}'", email);
                }

                user = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    IsActive = true,
                    Email = email,
                    NormalizedEmail = email.ToUpper(),
                    UserName = email,
                    NormalizedUserName = email.ToUpper(),
                    ObjectId = null,
                    ApplicationUserRoles = new List<Microsoft.AspNetCore.Identity.IdentityUserRole<Guid>>
                    {
                        new Microsoft.AspNetCore.Identity.IdentityUserRole<Guid> { RoleId = WellKnownRoleIds.Client }
                    }
                };

                await _usersRepository.CreateUserAsync(user);
            }

            return user;
        }

        /// <summary>
        /// Creates or updates all masterclients from the legal entities.
        /// </summary>
        /// <param name="syncMasterClients">Collection with legal entities to check the masterclient for.</param>
        private async Task CreateOrUpdateMasterClientsAsync(IList<SyncMasterClientUser> syncMasterClients)
        {
            // Preload all masterclients
            var existingMasterClients = await _masterClientsRepository.FindAllAsync();
            var masterClients = existingMasterClients.ToDictionary(mc => mc.Code, StringComparer.OrdinalIgnoreCase);

            var masterClientsToInsert = new List<MasterClient>();
            var masterClientsToUpdate = new List<MasterClient>();

            foreach (var syncMasterClient in syncMasterClients)
            {
                if (!masterClients.TryGetValue(syncMasterClient.MasterClientCode, out var masterClient))
                {
                    masterClient = new MasterClient
                    {
                        Code = syncMasterClient.MasterClientCode,
                        Name = syncMasterClient.MasterClientName,
                        IsActive = true
                    };

                    masterClientsToInsert.Add(masterClient);
                    masterClients.Add(masterClient.Code, masterClient);
                }
                else
                {
                    if (!masterClient.IsActive.GetValueOrDefault() || masterClient.Name != syncMasterClient.MasterClientName)
                    {
                        masterClient.IsActive = true;
                        masterClient.Name = syncMasterClient.MasterClientName;
                        masterClientsToUpdate.Add(masterClient);
                    }
                }
            }

            using var transaction = await _masterClientsRepository.DbContext.Database.BeginTransactionAsync();

            try
            {
                _logger.LogInformation("Saving masterclient data...");

                if (masterClientsToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} masterclients...", masterClientsToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(masterClientsToInsert, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No masterclients to insert");
                }

                if (masterClientsToUpdate.Count > 0)
                {
                    _logger.LogInformation("Starting BulkUpdate for {Count} masterclients...", masterClientsToUpdate.Count);
                    await _bulkOperationProvider.BulkUpdateAsync(masterClientsToUpdate, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkUpdate finished");
                }
                else
                {
                    _logger.LogInformation("No masterclients to update");
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Done saving data");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error saving data (Tx rolled back)");
                throw;
            }
        }

        /// <summary>
        /// Creates or updates all users from the masterclients.
        /// </summary>
        /// <param name="syncMasterClients">Collection with masterclients to check the emailaddresses for.</param>
        private async Task CreateOrUpdateUsersAsync(IList<SyncMasterClientUser> syncMasterClients)
        {
            // Preload all client portal users
            var usersByEmail = await _applicationUsersRepository
                                     .DbContext
                                     .Set<ApplicationUser>()
                                     .Where(user => user.ApplicationUserRoles.Any(role => role.RoleId == WellKnownRoleIds.Client))
                                     .ToDictionaryAsync(user => user.Email.ToUpperInvariant());

            var usersToInsert = new List<ApplicationUser>();
            var userAttributesToInsert = new List<UserAttribute>();
            var usersToUpdate = new List<ApplicationUser>();

            foreach (var masterClientUser in syncMasterClients)
            {
                var emailAddress = masterClientUser.UserEmail;

                if (!usersByEmail.ContainsKey(emailAddress.ToUpperInvariant()))
                {
                    if (emailAddress.Contains('@', StringComparison.Ordinal))
                    {
                        var newUser = new ApplicationUser
                        {
                            Id = Guid.NewGuid(),
                            IsActive = true,
                            Email = emailAddress.ToLower(),
                            NormalizedEmail = emailAddress.ToUpper(),
                            UserName = emailAddress.ToLower(),
                            NormalizedUserName = emailAddress.ToUpper(),
                            ObjectId = null,
                            ApplicationUserRoles = new List<Microsoft.AspNetCore.Identity.IdentityUserRole<Guid>>
                            {
                                new Microsoft.AspNetCore.Identity.IdentityUserRole<Guid> { RoleId = WellKnownRoleIds.Client }
                            }
                        };
                        usersByEmail.Add(emailAddress, newUser);

                        usersToInsert.Add(newUser);
                        userAttributesToInsert.Add(new UserAttribute
                        {
                            UserId = newUser.Id,
                            Key = UserAttributeKeys.InitialSyncAt,
                            Value = DateTime.UtcNow.ToString(GeneralConsts.DateTimeZoneFormat)
                        });
                    }
                    else
                    {
                        _logger.LogInformation("Skipped user with invalid email address {EmailAddress}", emailAddress);
                    }
                }
                else
                {
                    _logger.LogTrace("User with email address {EmailAddress} already exists", emailAddress);
                }
            }

            using var transaction = await _masterClientsRepository.DbContext.Database.BeginTransactionAsync();

            try
            {
                if (_masterClientsRepository.DbContext.Database.IsRelational())
                {
                    _masterClientsRepository.DbContext.Database.SetCommandTimeout(300);
                }

                _logger.LogInformation("Saving user data...");

                if (usersToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} users...", usersToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(usersToInsert, _masterClientsRepository.DbContext, includeGraph: true);
                    await _bulkOperationProvider.BulkInsertAsync(userAttributesToInsert, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No users to insert");
                }

                if (usersToUpdate.Count > 0)
                {
                    _logger.LogInformation("Starting BulkUpdate for {Count} users...", usersToUpdate.Count);
                    await _bulkOperationProvider.BulkUpdateAsync(usersToUpdate, _masterClientsRepository.DbContext, includeGraph: true);
                    _logger.LogInformation("BulkUpdate finished");
                }
                else
                {
                    _logger.LogInformation("No users to update");
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Done saving data");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error saving data (Tx rolled back)");
                throw;
            }
        }
    }
}
