import type { JS<PERSON> } from "react";
import { <PERSON><PERSON> } from "@netpro/design-system";
import { useLoaderData, useLocation } from "@remix-run/react";
import { Filter } from "lucide-react";
import { z } from "zod";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormSearch } from "~/components/FormSearch";
import { useUserColumns } from "~/features/users/hooks/useUserColumns";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementGetUsers } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Users",
    to: "/users",
  },
  title: "User Accounts",
}

export const loader = makeEnhancedLoader(async ({ json, request, getUserPreferences, enhancedURL }) => {
  const { tablePageSize } = await getUserPreferences()
  await middleware(["auth"], request);
  const usersResponse = await managementGetUsers({ headers: await authHeaders(request), query: {
    PageNumber: Number(enhancedURL.searchParams.get("page")) || undefined,
    Filter: enhancedURL.searchParams.get("search") ?? undefined,
    SortOrder: enhancedURL.searchParams.get("orderDirection") ?? undefined,
    SortBy: enhancedURL.searchParams.get("order") as ("Email" | "IsBlocked" | "PrimaryRoleLabel" | undefined) ?? undefined,
    PageSize: tablePageSize,
  } })

  return json({
    usersData: usersResponse.data,
  })
}, { authorize: ["users.view", "users.search"] })

const searchSchema = z.object({
  search: z.string().optional(),
})

export default function UsersLayout(): JSX.Element {
  const location = useLocation();
  const { usersData } = useLoaderData<typeof loader>()
  const { formMethods } = useFilterForm(searchSchema);
  const { columns } = useUserColumns();

  return (
    <CardContainer>
      <Authorized oneOf={["users.search"]}>
        <Form formMethods={formMethods}>
          <FilterRow>
            <div className="col-span-full flex flex-row items-center gap-2">
              <FormSearch name="search" formItemProps={{ className: "w-full" }} inputProps={{ placeholder: "Search owner or manager email address" }} />
              <Button size="sm" className="gap-1.5" type="submit">
                <Filter size={14} />
                Apply Filter(s)
              </Button>
            </div>
          </FilterRow>
        </Form>
      </Authorized>

      <EnhancedTableContainer>
        <EnhancedTable
          rowId="id"
          columns={columns}
          sheetURL="/users/"
          returnURL="/users/"
          data={usersData?.data}
          totalItems={usersData?.totalItemCount}
          defaultOpen={/^\/users\/./.test(location.pathname)}
        />
      </EnhancedTableContainer>
    </CardContainer>
  )
}
