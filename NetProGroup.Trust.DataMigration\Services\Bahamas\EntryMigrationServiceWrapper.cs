// <copyright file="EntryMigrationServiceWrapper.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Modules;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.DataMigration.Models.Bahamas;

namespace NetProGroup.Trust.DataMigration.Services.Bahamas
{
    /// <summary>
    /// Service for migrating entry data.
    /// </summary>
    public class EntryMigrationServiceWrapper : IEntityMigrator
    {
        private readonly ILogger<EntryMigrationServiceWrapper> _logger;
        private readonly EntityMigrationService _entityMigrationService;
        private readonly IModulesRepository _modulesRepository;
        private readonly IUserRepository _usersRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IServiceProvider _serviceProvider;
        private Module _module;
        private Jurisdiction _jurisdiction;

        /// <summary>
        /// Initializes a new instance of the <see cref="EntryMigrationServiceWrapper"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="entityMigrationService">The entity migration service.</param>
        /// <param name="modulesRepository">The modules repository.</param>
        /// <param name="usersRepository">The users repository.</param>
        /// <param name="jurisdictionsRepository">Instance of the JurisdictionsRepository.</param>
        /// <param name="serviceProvider"></param>
        public EntryMigrationServiceWrapper(
            ILogger<EntryMigrationServiceWrapper> logger,
            EntityMigrationService entityMigrationService,
            IModulesRepository modulesRepository,
            IUserRepository usersRepository,
            IJurisdictionsRepository jurisdictionsRepository,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _entityMigrationService = entityMigrationService ?? throw new ArgumentNullException(nameof(entityMigrationService));
            _modulesRepository = modulesRepository ?? throw new ArgumentNullException(nameof(modulesRepository));
            _usersRepository = usersRepository ?? throw new ArgumentNullException(nameof(usersRepository));

            _jurisdictionsRepository = jurisdictionsRepository;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Migrates entries asynchronously.
        /// </summary>
        /// <param name="migrationRecord">The migration record.</param>
        /// <param name="jobLock">The job lock.</param>
        public async Task MigrateEntitiesAsync(Domain.DataMigrations.DataMigration migrationRecord, LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));
            ArgumentNullException.ThrowIfNull(jobLock, nameof(jobLock));

            _jurisdiction ??= await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == migrationRecord.Region);
            _module ??= await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.EconomicSubstanceBahamas);

            var user = await _usersRepository.CheckUserByIdAsync(migrationRecord.StartedByUserId);

            await _entityMigrationService.MigrateEntityAsync<Entry>(migrationRecord, jobLock, MigrationConsts.Entries.CollectionName, MigrationConsts.Entries.DisplayName, async (entry) =>
            {
                using var scope = _serviceProvider.CreateScope();
                var entryMigrationService = scope.ServiceProvider.GetRequiredService<EntryMigrationService>();

                string region = migrationRecord.Region;

                var (success, errors) = await entryMigrationService.HandleEntry(entry, user, region, _module.Id, _module.Name, _jurisdiction.Id, _jurisdiction.Name);

                return (success, errors);
            }, entry =>
                new
                {
                    entry.Id,
                    CompanyCode = entry.Company,
                    entry.PeriodYear,
                    entry.CompanyData.MasterClientCode,
                    CompanyDataCode = entry.CompanyData.Code,
                    CompanyDataVpCode = entry.CompanyData.VPCode,
                    entry.InvoiceNumber
                });
        }
    }
}
