﻿// <copyright file="ISubmissionsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Tools;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Interface for submission operations within the application.
    /// </summary>
    public interface ISubmissionsAppService : IScopedService
    {
        /// <summary>
        /// Marks multiple submissions as paid or unpaid.
        /// </summary>
        /// <param name="submissionIds">The IDs of the submissions to update.</param>
        /// <param name="isPaid">True to mark as paid, false to mark as unpaid.</param>
        /// <returns>The updated submissions.</returns>
        Task<MarkSubmissionsAsPaidResponse> MarkSubmissionsAsPaidAsync(IEnumerable<Guid> submissionIds, bool isPaid);

        /// <summary>
        /// Marks multiple submissions as paid or unpaid based on company code and filing year.
        /// </summary>
        /// <param name="companyYearPairs">The list of company code and filing year pairs.</param>
        /// <param name="isPaid">True to mark as paid, false to mark as unpaid.</param>
        /// <param name="moduleId">The id of the module that this request is related to.</param>
        /// <returns>The updated submissions.</returns>
        Task<MarkSubmissionsAsPaidByCompanyYearResponse> MarkSubmissionsAsPaidByCompanyAndYearAsync(IEnumerable<CompanyFinancialYearDto> companyYearPairs,
            bool isPaid,
            Guid moduleId);

        /// <summary>
        /// Gets the paid status of submissions based on company code and filing year.
        /// </summary>
        /// <param name="companyYearPairs">The list of company code and filing year pairs.</param>
        /// <param name="moduleId">The id of the module that this request is related to.</param>
        /// <returns>The paid status for each company/year pair.</returns>
        Task<SubmissionsPaidStatusResponse> GetSubmissionsPaidStatusByCompanyAndYearAsync(IEnumerable<CompanyFinancialYearDto> companyYearPairs, Guid moduleId);

        /// <summary>
        /// Lists submissions using parameters in the request.
        /// </summary>
        /// <param name="legalEntityId">The ID of the legal entity (company) to get the submissions for.</param>
        /// <param name="moduleId">The ID of the module to get the submissions for.</param>
        /// <param name="request">The request DTO holding the parameters for the search.</param>
        /// <returns>The submissions as a paged list.</returns>
        Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(Guid legalEntityId, Guid moduleId, ListSubmissionsRequestDTO request);

        /// <summary>
        /// Lists submissions using parameters in the request.
        /// </summary>
        /// <param name="masterClientId">The ID of the master client to get the submissions for.</param>
        /// <param name="request">The request DTO holding the parameters for the search.</param>
        /// <returns>The submissions as a paged list.</returns>
        Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(Guid masterClientId, ListSubmissionsByMasterClientRequestDTO request);

        /// <summary>
        /// Starts a new submission in draft mode for the given legal entity (company), module and year as given in the model.
        /// </summary>
        /// <param name="model">The model with the parameters for the new submission.</param>
        /// <returns>The new submission.</returns>
        Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model);

        /// <summary>
        /// Gets the latest version of the submission.
        /// </summary>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <param name="includeFormDocument">Whether to include the form document in the response.</param>
        /// <param name="allowDeleted">Whether to include the deleted submissions in the response.</param>
        /// <returns>The submission.</returns>
        Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, bool includeFormDocument, bool allowDeleted = false);

        /// <summary>
        /// Updates the dataset in the revision of the submission.
        /// </summary>
        /// <param name="model">The model with the dataset to update.</param>
        /// <returns>The updated submission.</returns>
        Task<SubmissionDTO> UpdateSubmissionDataSetAsync(SubmissionDataSetDTO model);

        /// <summary>
        /// Submits the submission. This will make the submission final.
        /// </summary>
        /// <param name="model">The model with the parameters for the submission.</param>
        /// <returns>The submitted submission.</returns>
        Task<SubmissionDTO> SubmitSubmissionAsync(SubmitSubmissionDTO model);

        /// <summary>
        /// Reopens the submission by creating a new revision in draft status.
        /// </summary>
        /// <param name="model">The model with the parameters for the reopening.</param>
        /// <returns>The reopened submission.</returns>
        Task<SubmissionDTO> ReopenSubmissionAsync(ReopenSubmissionDTO model);

        /// <summary>
        /// Gets the years to use in the dropdown when startign a new submission.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity to get the years for.</param>
        /// <param name="moduleId">Id of the module to get the years for.</param>
        /// <returns>AvailableSubmissionYearsDTO.</returns>
        Task<AvailableSubmissionYearsDTO> GetAvailableSubmissionYears(Guid legalEntityId, Guid moduleId);

        /// <summary>
        /// Gets the years to use in the dropdown for a specific modules.
        /// </summary>
        /// <param name="moduleId">Id of the module to get the years for.</param>
        /// <returns>AllSubmissionYearsDTO.</returns>
        Task<AllSubmissionYearsDTO> GetAllSubmissionYears(Guid moduleId);

        /// <summary>
        /// Lists submissions using parameters in the request.
        /// </summary>
        /// <param name="request">The request DTO holding the parameters for the search.</param>
        /// <returns>The submissions as a paged list.</returns>
        Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsAsync(SearchSubmissionsRequestDTO request);

        /// <summary>
        /// Mark the submission as deleted.
        /// </summary>
        /// <remarks>
        /// This will mark the submission as deleted if the status is not Submitted.
        /// </remarks>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteSubmissionAsync(Guid submissionId);

        /// <summary>
        /// Update the general information for a submission entity.
        /// </summary>
        /// <remarks>
        /// Used to check and update the basic configuration for submissions.
        /// </remarks>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <param name="data">The data used to update the general information for the submission.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task UpdateSubmissionGeneralInformationAsync(Guid submissionId, UpdateSubmissionInformationDTO data);

        /// <summary>
        /// Update the general information for a submission entity.
        /// </summary>
        /// <remarks>
        /// Used to check and update the basic configuration for submissions.
        /// </remarks>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <param name="data">The data used to update the general information for the submission.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task UpdateSubmissionGeneralInformationManagementAsync(Guid submissionId, UpdateSubmissionInformationDTO data);

        /// <summary>
        /// Searches for the submissions for a module with extra search criteria (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A paged list with <see cref="ListSubmissionDTO"/>.</returns>
        Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsForPanamaAsync(FilterSubmissionsRequestDTO request);

        /// <summary>
        /// Searches for the submissions for a module with extra search criteria (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A paged list with <see cref="ListSubmissionBahamasDTO"/>.</returns>
        Task<IPagedList<ListSubmissionBahamasDTO>> SearchSubmissionsForBahamasAsync(FilterSubmissionsRequestForBahamasDTO request);

        /// <summary>
        /// Generate a zip file containing the submission files.
        /// </summary>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <returns>The generated zip file as ZipFileDTO.</returns>
        Task<ZipFileDTO> RetrieveSubmissionDocumentsAsync(Guid submissionId);

        /// <summary>
        /// Generate a zip file containing the submissions files.
        /// </summary>
        /// <param name="submissionIds">The submission ids as a list of Guid.</param>
        /// <returns>The generated zip file as ZipFileDTO.</returns>
        Task<ZipFileDTO> RetrieveSubmissionsDocumentsAsync(List<Guid> submissionIds);

        /// <summary>
        /// Searches for BVI submissions using the search parameters object.
        /// </summary>
        /// <param name="request">The search parameters object.</param>
        /// <returns>The found submissions.</returns>
        Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsForBVIAsync(SearchSubmissionsRequestDTO request);

        /// <summary>
        /// Returns the count of RFIs for a master client.
        /// </summary>
        /// <param name="masterClientId">The ID of the module for which to count submissions.</param>
        /// <param name="entityId">The ID of the entity for which to count submissions.</param>
        /// <returns>A count of submissions with status submitted or revision.</returns>
        Task<int> GetSubmissionsWithRFICountAsync(Guid masterClientId, Guid entityId);

        /// <summary>
        /// Gets the number of unpaid annual invoices.
        /// </summary>
        /// <param name="masterClientId">The ID of the master client to check for pending payment submissions.</param>
        /// <returns>The number of submissions with pending payments.</returns>
        Task<int> GetUnpaidSubmissionsCountAsync(Guid masterClientId);
    }
}
