import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import type { ListApplicationUsersDTO, ManagementGetUsersData } from "~/services/api-generated";

type SortableColumns = NonNullable<NonNullable<ManagementGetUsersData["query"]>["SortBy"]>;

export const sortableColumnNames = Object.keys({
  Email: null,
  IsBlocked: null,
  SyncStatus: null,
  InitialSyncAt: null,
  RegistrationDate: null,
  PrimaryRoleLabel: null,
} satisfies Record<SortableColumns, null>)
const makeColumn = makeMakeColumn<SortableColumns, ListApplicationUsersDTO>(sortableColumnNames)

export function useUserColumns() {
  const formatColDate = useFormatColDate();
  
  const columns = [
    makeColumn({ 
      header: "Email", 
      id: "Email", 
      accessorKey: "email", 
      accessorFn: ({ email }) => email?.toLowerCase() 
    }),
    makeColumn({ 
      header: "Type", 
      id: "PrimaryRoleLabel", 
      accessorKey: "primaryRoleLabel" 
    }),
    makeColumn({ 
      header: "Blocked", 
      id: "IsBlocked", 
      accessorKey: "isBlocked", 
      cell: data => data.row.original.isBlocked ? "Yes" : "No" 
    }),
    makeColumn({ 
      header: "Sync Status", 
      id: "SyncStatus", 
      accessorKey: "syncStatus" 
    }),
    makeColumn({ 
      header: "Registration Date", 
      id: "RegistrationDate", 
      accessorKey: "registrationDate", 
      cell: formatColDate("registrationDate", { timezone: "UTC", fallback: "" }) 
    }),
    makeColumn({ 
      header: "Initial Sync Date", 
      id: "InitialSyncAt", 
      accessorKey: "initialSyncAt", 
      cell: formatColDate("initialSyncAt", { timezone: "UTC", fallback: "" }) 
    }),
  ];

  return { columns }
}
